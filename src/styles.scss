/* You can add global styles to this file, and also import other style files */
// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@use '@angular/material' as mat;
// Plus imports for other components in your app.

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat.core();

// Define the palettes for your theme using the Material Design palettes available in palette.scss
// (imported above). For each palette, you can optionally specify a default, lighter, and darker
// hue. Available color palettes: https://material.io/design/color/
$theme-primary: mat.define-palette(mat.$indigo-palette);
$theme-accent: mat.define-palette(mat.$pink-palette, A200, A100, A400);

// The warn palette is optional (defaults to red).
$theme-warn: mat.define-palette(mat.$red-palette);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as "color" or "typography".
$theme: mat.define-light-theme((
  color: (
    primary: $theme-primary,
    accent: $theme-accent,
    warn: $theme-warn,
  ),
  typography: mat.define-typography-config(),
));

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
@include mat.all-component-themes($theme);
@import "material-ui";
@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap');
$inter-font-family: "Roboto", serif;

body {
  font-family: $inter-font-family;
}

.sql-hl-keyword {
  color: brown;
  font-weight: bold;
}
.sql-hl-string {
  color: green;
  font-weight: bold;
}
.sql-hl-function {
  color: blue;
  font-weight: bold;
}
.sql-hl-number {
  color: blue;
}

/* Định dạng chung cho tất cả các tiêu đề */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  line-height: 1.3;
  font-weight: 600;
}

/* Tiêu đề cấp 1 */
h1 {
  font-size: 2.2em;
  padding-bottom: 0.3em;
}

/* Tiêu đề cấp 2 */
h2 {
  font-size: 1.8em;
  padding-bottom: 0.3em;
}

/* Tiêu đề cấp 3 */
h3 {
  font-size: 1.5em;
}

/* Tiêu đề cấp 4 */
h4 {
  font-size: 1.3em;
}

/* Tiêu đề cấp 5 */
h5 {
  font-size: 1.1em;
}

/* Liên kết */
a {
  color: #0366d6;
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  text-decoration: underline;
}

