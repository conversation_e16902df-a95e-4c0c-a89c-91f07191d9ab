import { Injectable } from '@angular/core';
import {HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest} from "@angular/common/http";
import {catchError, Observable, throwError} from "rxjs";
import {LogInService} from "../shared/services/log-in.service";

@Injectable({
  providedIn: 'root'
})
export class AuthInterceptor implements HttpInterceptor{

  constructor(private loginService: LogInService) { }

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>>
  {
    console.log('AuthInterceptor is called');

    // Clone the request object
    let newReq = req.clone();

    console.log(req, next)

    // Request
    //
    // If the access token didn't expire, add the Authorization header.
    // We won't add the Authorization header if the access token expired.
    // This will force the server to return a '401 Unauthorized' response
    // for the protected API routes which our response interceptor will
    // catch and delete the access token from the local storage while logging
    // the user out from the app.
    // if ( this._authService.accessToken && !AuthUtils.isTokenExpired(this._authService.accessToken) )
    if ( this.loginService.accessToken)
    {
      newReq = req.clone({
        headers: req.headers.set('Authorization', 'Bearer ' + this.loginService.accessToken)
        // .set('TIMEZONE', moment())
      });
    }


    // Response
    return next.handle(newReq).pipe(
      catchError((error) => {
        console.log(error)
        // Catch '401 Unauthorized' responses
        if ( error instanceof HttpErrorResponse && (error.status === 0 ||error.status === 401 || (error.url.includes('/account') && error.status === 500 )))
        {
          // Sign out
          this.loginService.signOut();

          // Reload the app
          // location.reload();
        }

        return throwError(error);
      })
    );
  }
}
