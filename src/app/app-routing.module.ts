import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {DiagramComponent} from "./components/diagram/diagram.component";
import {ChatMessageComponent} from "./components/chat-message/chat-message.component";
import {AuthComponent} from "./modules/auth/auth.component";
import {QueryGptComponent} from "./modules/query-gpt/query-gpt.component";
import {AuthGuard} from "./core/auth.guard";

const routes: Routes = [
  { path: '', redirectTo: '/auth/sign-in', pathMatch: 'full' },
  { path: 'auth',loadChildren: () => import('./modules/auth/auth.module').then(m => m.AuthModule), component: AuthComponent },
  { path: 'query-gpt', loadChildren: () => import('./modules/query-gpt/query-gpt.module').then(m => m.QueryGptModule), component: QueryGptComponent },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
