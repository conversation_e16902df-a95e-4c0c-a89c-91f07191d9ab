import {NgModule} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';

import {AppRoutingModule} from './app-routing.module';
import {AppComponent} from './app.component';
import {SocketService} from './socket.service';
import {BrowserAnimationsModule} from "@angular/platform-browser/animations";
import {SharedModule} from "@shared/shared.module";
import {ChatMessageComponent} from './components/chat-message/chat-message.component';
import { AuthComponent } from './modules/auth/auth.component';
import { QueryGptComponent } from './modules/query-gpt/query-gpt.component';
import { ShowFeedbackComponent } from './components/show-feedback/show-feedback.component';
import {HTTP_INTERCEPTORS, HttpClientModule} from "@angular/common/http";
import {AuthInterceptor} from "./core/auth.interceptor";
import { DateAgoPipe } from './shared/pipes/date-ago.pipe';
import {MatSnackBarModule} from "@angular/material/snack-bar";
import {ImageGalleryComponent} from "./shared/components/image-gallery/image-gallery.component";
import {ImageGridComponent} from "./shared/components/image-grid/image-grid.component";
import {SafeHtmlPipe} from "./shared/pipes/safe-html.pipe";
import { DateFormatterPipe } from './shared/pipes/date-formatter.pipe';
import { ComplexMessageComponent } from './shared/components/complex-message/complex-message.component';

@NgModule({
    declarations: [
        AppComponent,
        AuthComponent,
        QueryGptComponent,
        ShowFeedbackComponent,
        DateAgoPipe,
        ImageGalleryComponent,
        ImageGridComponent,
        SafeHtmlPipe,
        DateFormatterPipe,
        ComplexMessageComponent,
    ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    SharedModule,
    BrowserAnimationsModule,
    HttpClientModule,
    MatSnackBarModule,

  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    SocketService,
  ],
  exports: [
    ImageGalleryComponent,
    ImageGridComponent,
    SafeHtmlPipe,
    DateFormatterPipe,
    ComplexMessageComponent
  ],

  bootstrap: [ChatMessageComponent]
})
export class AppModule {
}
