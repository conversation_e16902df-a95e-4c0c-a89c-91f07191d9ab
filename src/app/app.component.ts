import {
  Component, ElementRef, OnInit, Renderer2,
} from '@angular/core';
import {ActivatedRoute, NavigationEnd, Router} from '@angular/router';
import {LogInService} from "./shared/services/log-in.service";
import {filter} from "rxjs";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit{

  idActiveLink: number = 0;
  showDiv: any;
  showSidebar = false

  roleActive = "CEO"

  roleSignIn = [{
    title: "Sign in as CEO",
    name: "CEO"
  },{
    title: "Sign in as BU1 manager",
    name: "BU1"
  },{
    title: "Sign in as staff",
    name: "Staff"
  }];

  constructor(private route: ActivatedRoute,
              private router: Router,
              private renderer: Renderer2,
              private el: ElementRef,
              private loginService: LogInService,) {
    this.detectiveLink();
  }

  ngOnInit() {
    this.checkLogin()
  }

  navigateUrl(path) {
    // Thay đổi đường dẫn URL khi bạn bấm vào thẻ Data Schema
    this.router.navigate([path]);
    this.detectiveLink();
  }

  detectiveLink() {
    this.router.events.subscribe(() => {
      switch (this.router.url) {
        case '/diagram':
          this.idActiveLink = 1;
          break;
        default :
          this.idActiveLink = 0;
          break;
      }
    });
  }

  toggleDiv() {
    this.showDiv = !this.showDiv;
  }

  changeRole(role) {
    this.showDiv = !this.showDiv;
    this.roleActive = role;
    this.loginService.sendLoginName(role);
  }

  handleShowSidebar(status) {
    this.showSidebar = status
  }

  private checkLogin() {
    const token = localStorage.getItem('accessToken') || sessionStorage.getItem('accessToken');
    if (token) {
      // this.router.navigate(['/query-gpt/chat']);
      this.loginService.getAccount().subscribe((res: any) => {
        this.loginService.updateUserInfoInStorage(res.user, !!localStorage.getItem('accessToken'));
      });
    } else  {
      this.router.navigate(['/auth/sign-in']);
    }

  }
}
