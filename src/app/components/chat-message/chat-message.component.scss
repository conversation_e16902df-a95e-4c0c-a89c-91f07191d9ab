.chat-frame {
  max-width: 100%;
  height: calc(100vh - 22px);
}

.chat-input {
  // Removed border since individual elements now have their own styling

  .bg-white {
    border: 1px solid rgba(0, 0, 0, .15);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  textarea {
    border: none;
    outline: none;
    resize: none;
    font-family: inherit;
  }
}

.typing-animation {
  height: 20px;
  width: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dot {
  height: 8px;
  width: 8px;
  background-color: #333;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1.0); }
}

.grid-item {
  animation-delay: 1s;
}

.chat-content-container {
  height: calc(100vh - 80px);
}

ng-scrollbar.scroll-custom {
  --scrollbar-size: 8px;
  --scrollbar-thumb-color: #E6E6E6;
  --scrollbar-border-radius: 4px;
  --scrollbar-padding: 0px;
}

::ng-deep .ng-scroll-content{
  display: block !important;
}

.chat-input-container {
  position: absolute;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
}

@media (min-width: 768px) {
  .chat-content-container {
    height: calc(100vh - 200px);
  }
}

.cus-text-area {
  height: 200px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px;
  gap: 4px;
  background: #f9f9f9;
  border: 1px solid #33383F
}

//@media screen and (-webkit-min-device-pixel-ratio:0) {
//  select,
//  textarea,
//  input {
//    font-size: 16px;
//  }
//}

.msg-bubble {
  @apply flex items-center rounded-3xl mb-1 relative 2xl:max-w-[80vw] break-words whitespace-pre-wrap;
}

// Primary colors for send button
.bg-primary {
  background-color: #7241FF;
}

.bg-primaryLight {
  background-color: #8E66FF;
}

.hover\:bg-primaryLight:hover {
  background-color: #8E66FF;
}

// Send button with dynamic hover color
.send-button:hover {
  background-color: var(--hover-color) !important;
}


