import { animate, style, transition, trigger } from "@angular/animations";
import { AfterViewInit, Component, ElementRef, Injector, Ng<PERSON>one, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { BaseComponent } from "@core/base.component";
import { COMPLEX_MESSAGE_TYPE, TYPE_MESSAGE_SOCKET } from "@shared/app.constant";
import { filter, of, Subscription, switchMap } from "rxjs";
import { v4 as uuidv4 } from "uuid";
import { ChatConversationService } from "../../shared/services/chat-conversation.service";
import { FeedbackService } from "../../shared/services/feedback.service";
import { ImageZoomService } from "../../shared/services/image-zoom.service";
import { SocketService } from "../../socket.service";

@Component({
  selector: 'app-chat-message',
  templateUrl: './chat-message.component.html',
  styleUrls: ['./chat-message.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({opacity: 0}),
        animate('0.5s 500ms ease-out', style({opacity: 1}))
      ]),
    ]),
  ],
})
export class ChatMessageComponent extends BaseComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('scrollRef') scrollRef: any;
  @ViewChild('inputTextArea') inputTextArea: ElementRef;
  @ViewChild('chatContainer') chatContainer: ElementRef;

  messageInput: string = '';
  listChatMessage: any[] = [];
  private subscription: Subscription = new Subscription();
  isTyping: boolean = false;
  conversationId = uuidv4();
  aiId = "";
  domain = "";
  chatTextSize: string = "14px";
  chatLineHeight: string = "unset"
  userColor: string = "white";
  userBackground: string = "#7241ff";
  assistantColor: string = "black";
  assistantBackground: string = '#F3F4F6';
  svgColor: string = '#007bff';
  widgetHeaderBackgroundColor: string = '#273272';
  welcomeMessage: string = null;
  private hasCallStartFlow: boolean = false;
  inputPlaceholder: string = "Write your question";
  userData: any;
  userInfo: any;
  private listener: (event) => void;
  assistantAvatar: any = null;
  isStreaming: boolean[] = [false];
  partitionOrdinal: number;
  isLoadingImage: boolean;
  private newChat: boolean = true;
  private streamingQueue: any[] = [];
  private isProcessingImage: boolean = false;
  private imageMarkdownBuffer: string = "";
  isSendingMessage = false;
  private complexMessageQueue: any[] = [];
  isAutoScrollDown: boolean = true;
  private scrollThreshold: number = 10; // Threshold in pixels to determine if user has scrolled away from bottom

  constructor(private socketService: SocketService,
              private feedbackService: FeedbackService,
              private conversationService: ChatConversationService,
              private imageZoomService: ImageZoomService,
              injector: Injector,
              private zone: NgZone
  ) {
    super(injector);
  }

  ngOnInit(): void {
    // Load userInfo from localStorage on init
    const savedUserInfo = localStorage.getItem('userInfo');
    if (savedUserInfo) {
      try {
        this.userInfo = JSON.parse(savedUserInfo);
      } catch (error) {
      }
    }

    this.listenMessageSocket();
    this.listenPostMessage();
    this.imageZoomService.init();
  }

  ngAfterViewInit() {
    // Add scroll event listener to detect when user scrolls away from bottom
    setTimeout(() => {
      try {
        // Truy cập trực tiếp vào phần tử DOM của ng-scrollbar
        const scrollbarElement = document.querySelector('.ng-scroll-viewport');
        if (scrollbarElement) {
          // Lưu ý: Sử dụng arrow function để giữ nguyên context của this
          scrollbarElement.addEventListener('scroll', (event) => this.onUserScroll(event));
        } else {
        }
      } catch (error) {
      }
    }, 1000); // Tăng thời gian chờ để đảm bảo DOM đã được render
  }

  ngOnDestroy() {
    localStorage.setItem('chatmess', JSON.stringify(this.listChatMessage));
    this.socketService.disconnectCs();
    this.socketService.disconnectMessage();
    this.subscription.unsubscribe();
    window.removeEventListener('message', this.listener);

    // Không cần xóa event listener vì chúng ta đã sử dụng arrow function
    // và element sẽ tự động được dọn dẹp khi component bị hủy
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.altKey && event.key === 'Enter' || event.shiftKey && event.key === 'Enter') {
      event.preventDefault(); // Prevent a newline from being added
      let value = (event.target as HTMLTextAreaElement).value;
      let selectionStart = (event.target as HTMLTextAreaElement).selectionStart;
      let selectionEnd = (event.target as HTMLTextAreaElement).selectionEnd;

      // Update the textarea value to include the new line
      (event.target as HTMLTextAreaElement).value =
        value.substring(0, selectionStart) + '\n' + value.substring(selectionEnd);

      // Update the cursor position after the new line
      (event.target as HTMLTextAreaElement).selectionStart =
        (event.target as HTMLTextAreaElement).selectionEnd = selectionStart + 1;
      this.updateTextAreaHeight();
    } else if (event.key === 'Enter') {
      event.preventDefault(); // Prevent a newline from being added
      this.sendMessage(this.messageInput);
      this.updateTextAreaHeight();
    }
  }

  updateTextAreaHeight() {
    // Calculate the number of rows in the textarea based on the content
    const textarea = document.getElementById('chat');
    if (textarea) {
      textarea.style.height = 'auto'; // Reset the height to auto
      textarea.style.height = textarea.scrollHeight + 'px'; // Set the height to match the content
    }

    if (textarea && this.messageInput.trim() === '') {
      textarea.style.height = 'auto';
    }
  }

  sendMessage(msg: string, action: any = null) {
    if (this.isTyping) {
      return;
    }

    if (((msg && msg.trim() !== '') || action) && !this.isSendingMessage) {
      this.isSendingMessage = true;
      msg = msg.trim().replace(/[\r\n]+/g, ' ');

      let message = msg;
      let actionId = '';

      if (action) {
        if (action.link) {
          window.open(action.link, '_blank');
        }
        if (action.value) {
          message = action.value.trim().replace(/[\r\n]+/g, ' ');
          actionId = action.action_id;
        } else {
          return;
        }
      }

      const textAliases = this.findTextAliasesInteractiveButton(message);
      if (textAliases.length > 0) {
        if (textAliases[0].link) {
          window.open(textAliases[0].link, '_blank');
        }
        if (textAliases[0].value) {
          message = textAliases[0].value.trim().replace(/[\r\n]+/g, ' ');
          actionId = textAliases[0].actionId;
        } else {
          return;
        }
      }

      if (this.listChatMessage.length > 0) {
        const lastMessage = this.listChatMessage[this.listChatMessage.length - 1];
        if (lastMessage.content && lastMessage.content.send_type === 'complex') {
          this.listChatMessage.pop();
          let content = '';
          if (lastMessage.content.complex_type === COMPLEX_MESSAGE_TYPE.INTERACTIVE) {
            content = lastMessage.content.text.trim();
          }
          if (lastMessage.content.complex_type === COMPLEX_MESSAGE_TYPE.RICH_CARD) {
            content = lastMessage.content.subtitle.trim();
          }
          if (lastMessage.content.buttons && lastMessage.content.buttons.length > 0) {
            const buttonValues = lastMessage.content.buttons
              .map(button => button.value)
              .filter(value => value)
              .join('\n- ');
            if (buttonValues) {
              content += '\n- ' + buttonValues;
            }
          }
          this.listChatMessage.push({
            role: 'assistant',
            content: content,
            typeMessage: 'text',
            created_at: new Date().toISOString(),
          });
        }
      }

      this.listChatMessage.push({
        role: 'user',
        content: textAliases.length > 0 ? this.extractUrls(msg.trim(), 'user') : this.extractUrls(message.trim(), 'user'),
        created_at: new Date().toISOString(),
      });

      const body = {
        author: {
          type: 'user',
          data_info: this.userInfo,
        },
        content: message,
        interactive: Boolean(actionId),
        action_id: actionId,
        internal: false,
        partition_ordinal: this.partitionOrdinal
      };

      this.isTyping = true;
      this.isSendingMessage = true;
      this.socketService.sendMessage(body, this.conversationId);
      this.messageInput = '';
      this.scrollToBottomOfNgScrollBar();
    }
  }

  scrollToBottomOfNgScrollBar(): void {
    if (this.isAutoScrollDown) {
      setTimeout(() => {
        try {
          // Phương pháp 1: Sử dụng scrollRef nếu có thể
          if (this.scrollRef && this.scrollRef.scrollTo) {
            this.scrollRef.scrollTo({bottom: 0, duration: 0});
            return;
          }

          // Phương pháp 2: Sử dụng DOM trực tiếp nếu phương pháp 1 không hoạt động
          const scrollViewport = document.querySelector('.ng-scroll-viewport');
          if (scrollViewport) {
            scrollViewport.scrollTop = scrollViewport.scrollHeight;
          }
        } catch (error) {
        }
      }, 100);
    }
  }

  /**
   * Handles scroll events to detect if user has scrolled away from bottom
   * @param event Scroll event
   */
  private onUserScroll(event: Event): void {
    try {
      const element = event.target as HTMLElement;
      if (!element) return;

      const scrollPosition = element.scrollTop + element.clientHeight;
      const scrollHeight = element.scrollHeight;
      const distanceFromBottom = scrollHeight - scrollPosition;

      // Sử dụng zone.run để đảm bảo Angular phát hiện thay đổi
      this.zone.run(() => {
        // Nếu người dùng đã cuộn gần đáy (trong khoảng 20px), bật tự động cuộn
        if (distanceFromBottom < 10) {
          this.isAutoScrollDown = true;
        }
        // Nếu người dùng đã cuộn lên trên vượt quá ngưỡng, tắt tự động cuộn
        else if (distanceFromBottom > this.scrollThreshold) {
          this.isAutoScrollDown = false;
        }
      });
    } catch (error) {
    }
  }

  private listenMessageSocket() {
    this.subscription.add(
      this.socketService.connectedSocketSuccessMessage$.pipe(
        filter((value) => value === 'success'),
        switchMap(() => this.socketService.listenEventMessage('partition_ordinal')),
      ).subscribe({
        next: (message: any) => {
          if (message?.conversation_id === this.conversationId) {
            this.partitionOrdinal = message.partition_ordinal;
            if (!this.hasCallStartFlow) {
              this.socketService.callStartFlow({
                author: {
                  type: 'user'
                },
                content: "",
                internal: false,
                debug: false,
                preview: false,
                conversationId: this.conversationId,
                partition_ordinal: this.partitionOrdinal
              });
              this.hasCallStartFlow = true;
            }
          }
        }
      })
    )


    this.subscription.add(
      this.socketService.connectedSocketSuccessMessage$.pipe(
        switchMap((value) => {
          if (value === 'success') {
            this.socketService.joinRoomConversationMessage({
              prevConversationId: this.conversationId,
              conversationId: this.conversationId,
            })
            return of(value)
          }
          return of(null);
        }),
        switchMap(value => {
          if (value === 'success' && !this.socketService.connectedFlagMessage$.value) {
            this.socketService.connectedFlagMessage$.next(true);
            return this.socketService.listenEventMessage('message');
          }
          return of(null);
        })
      ).subscribe({
        next: (res: any) => {
          if (res && res.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_CS_CHAT) {
            if (this.conversationId === res.content.conversation_id) {
              if (res.content.content.content) {
                const item = res.content.content;
                const author = item.author ? JSON.parse(item.author) : null;
                const typeMessage = this.classifyMessage(item.content)
                const showSource = this.isShowSource(item.content)
                const lastMessage = this.listChatMessage[this.listChatMessage.length - 1];

                // Khi nhận tin nhắn mới, đặt isAutoScrollDown = true
                this.isAutoScrollDown = true;

                if (item.content.includes('DONE')) {
                  this.newChat = true;
                  const lastQueueItem = lastMessage.queue[lastMessage.queue.length - 1];
                  const cleanedQueueItem = lastQueueItem.replace(/\n+$/, '');
                  this.listChatMessage[this.listChatMessage.length - 1].queue[lastMessage.queue.length - 1] = cleanedQueueItem;
                  return; // Dừng luôn việc xử lý
                } else {
                  if (this.newChat) {
                    this.newChat = false;
                    const queue = this.checkTheVoid(item.content)
                    const msg = {
                      ...item,
                      summary: author ? this.extractUrls(author?.summary ?? '') : null,
                      role: author ? author?.type : null,
                      user: author ? author?.user : null,
                      typeMessage: typeMessage,
                      showSource: showSource,
                      queue: queue ? [queue] : [],
                      content: "",
                      haveMessage: Boolean(((this.extractUrls(item.content, author ? author?.type : null) || '').match(/<img\s+[^>]*src="([^"]*)"[^>]*>/g) || []).length),
                      listImages: typeMessage == "grid" || typeMessage == "gallery" ? this.extractImages(item.content) : "",
                      listTextSources: showSource ? this.extractSources(item.content) : [],
                      createAt: new Date().toISOString(),
                    }
                    this.listChatMessage.push(msg);
                    this.isTyping = false;
                    this.isAutoScrollDown = true; // Enable auto-scrolling for new messages
                    this.streamingQueue.push(this.listChatMessage.length - 1);
                    if (!this.isStreaming[this.listChatMessage.length - 1] && this.streamingQueue[0] == this.listChatMessage.length - 1) {
                      this.isStreaming[this.listChatMessage.length - 1] = true;
                      this.streamText(this.listChatMessage.length - 1); // Start streaming if not already in progress
                    }
                  } else {
                    lastMessage.queue.push(item.content); // Add the new text chunk to the queue
                    if (!this.streamingQueue.includes(this.listChatMessage.length - 1)) {
                      this.streamingQueue.push(this.listChatMessage.length - 1);
                    }
                    if (!this.isStreaming[this.listChatMessage.length - 1] && this.streamingQueue[0] == this.listChatMessage.length - 1) {
                      this.isStreaming[this.listChatMessage.length - 1] = true;
                      this.streamText(this.listChatMessage.length - 1); // Start streaming if not already in progress
                    }
                    // New message received, enable auto-scrolling
                    this.isAutoScrollDown = true;
                    lastMessage.typeMessage = typeMessage,
                      lastMessage.listImages = typeMessage === "grid" || typeMessage === "gallery" ? this.extractImages(item.content) : "",
                      lastMessage.showSource = showSource
                    lastMessage.listTextSources = showSource ? this.extractSources(item.content) : []
                  }
                }
              }
            }
          }
          if (res && res.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_FLOW_START) {
            if (this.conversationId === res.content.conversation_id) {
              const data = res.content.content;
              this.listChatMessage.push({
                role: 'assistant',
                typeMessage: "text",
                content: data.message_response,
              })
              this.isTyping = false;
            }
          }
          if (res && res.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_COMPLEX_CS_CHAT) {
            const data = res.content.content;
            const content = JSON.parse(data.content);
            const complexMessage = {
              ...data,
              role: 'user',
              content,
              typeMessage: 'complex',
              created_at: new Date().toISOString(),
              listImages: [],
              listTextSources: []
            };

            if (this.isStreaming.some(isStreaming => isStreaming)) {
              this.complexMessageQueue.push(complexMessage);
            } else {
              this.listChatMessage.push(complexMessage);
            }
          }
          if (res && res.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_CS_CHAT_PAUSE) {
            if (this.conversationId === res.content.conversation_id) {
              const data = res.content.content;
              if (data) {
                this.isTyping = false
                this.cdr.detectChanges()
              }
            }
          }
          if (res && res.type === TYPE_MESSAGE_SOCKET.SAVE_USER_INFO) {
            if (this.conversationId === res.content.conversation_id) {
              const userData = res.content.user_info;
              if (userData) {
                this.userInfo = userData;
                localStorage.setItem('userInfo', JSON.stringify(userData));
              }
            }
          }
        }
      })
    )

    this.subscription.add(
      this.socketService.connectedSocketSuccessCs$.pipe(
        switchMap((value) => {
          if (value === 'success') {
            this.socketService.callSettingDefault(this.conversationId);
            const prevChatMessage = JSON.parse(localStorage.getItem('chatmess') || '[]');
            if (!prevChatMessage || (prevChatMessage && prevChatMessage.length === 0)) {
              this.socketService.joinRoomConversationCs({
                prevConversationId: this.conversationId,
                conversationId: this.conversationId
              })
            } else {
              this.listChatMessage = prevChatMessage;
            }
            return this.socketService.listenEventCs('message');
          }
          return of(null);
        })
      ).subscribe({
        next: (res: any) => {
          if (res && res.type === TYPE_MESSAGE_SOCKET.GET_SETTING_AI) {
            if (this.conversationId === res.content.conversation_id) {
              // this.welcomeMessage = JSON.parse(res.content.settings)?.widget.welcome_message ?? null;
              this.inputPlaceholder = JSON.parse(res.content.settings)?.widget.input_placeholder;
              // if(this.welcomeMessage.trim().length > 0){
              //   if (this.listChatMessage.length <= 0) {
              //     this.listChatMessage = [
              //       {
              //         role: 'assistant',
              //         typeMessage: "text",
              //         content: this.welcomeMessage,
              //       }
              //     ]
              //   }
              this.isTyping = false;
              // }
            }
          }
          if (res && res.type === TYPE_MESSAGE_SOCKET.NEW_MESSAGE_HUMAN_REPLY) {
            if (this.conversationId === res.content.conversation_id) {
              const data = res.content.data_reply;
              const author = data.author ? JSON.parse(data.author) : null;
              const typeMessage = this.classifyMessage(data.content);
              const showSource = this.isShowSource(data.content);

              const humanReplyMessage = {
                role: author ? author.type : 'assistant',
                content: this.extractUrls(data.content, author ? author.type : 'assistant'),
                typeMessage: typeMessage,
                created_at: new Date().toISOString(),
                showSource: showSource,
                listTextSources: showSource ? this.extractSources(data.content) : [],
                listImages: typeMessage === "grid" || typeMessage === "gallery" ? this.extractImages(data.content) : ""
              };
              this.listChatMessage.push(humanReplyMessage);
              this.isTyping = false;
              this.isSendingMessage = false;
            }
          }
        }
      })
    )
  }

  private listenPostMessage() {
    window.removeEventListener('message', this.listener)
    this.listener = (event) => {
      const data = event.data;
      if (data.action === "refresh") {
        this.listChatMessage = []
        this.isTyping = false;
        const prevConversationId = this.conversationId
        this.conversationId = uuidv4();
        this.socketService.callSettingDefault(this.conversationId);
        this.socketService.joinRoomConversationCs({
          prevConversationId,
          conversationId: this.conversationId
        })
        this.socketService.joinRoomConversationMessage({
          prevConversationId,
          conversationId: this.conversationId
        })
        this.socketService.callStartFlow({
          author: {
            type: 'user'
          },
          content: "",
          internal: false,
          debug: false,
          preview: false,
          conversationId: this.conversationId,
          partition_ordinal: this.partitionOrdinal
        });
      }

      if (data.type == "addUser") {
        this.userInfo = data.userData;
      }

      if (data.type == "clearUser") {
        this.userInfo = null;
        localStorage.removeItem('userInfo');
      }

      if (data.type == "domain") {
        this.domain = data.domain;
      }

      if (data.type == "ai_id") {
        this.aiId = data.id;
      }

      if (data.type == "attr") {
        const attr = data.attr;
        this.chatTextSize = attr.chatTextSize;
        this.chatLineHeight = attr.chatLineHeight;
        this.userColor = attr.userColor;
        this.userBackground = attr.userBackground;
        this.assistantColor = attr.assistantColor;
        this.assistantBackground = attr.assistantBackground;
        this.svgColor = attr.bubbleColor;
        this.assistantAvatar = attr.assistantAvatar;
        this.widgetHeaderBackgroundColor = attr.widgetHeaderBackgroundColor;
      }

      if (this.aiId && this.domain) this.setupChatChannel();
    }
    window.addEventListener('message', this.listener);
  }

  private setupChatChannel(user?: any) {
    if (!this.listChatMessage || this.listChatMessage && this.listChatMessage.length === 0) {
      this.listChatMessage = JSON.parse(localStorage.getItem('chatmess') || '[]');
    }
    this.socketService.connectSocketCs({ai_id: this.aiId, domain: this.domain, user});
    this.socketService.connectSocketMessage({ai_id: this.aiId, domain: this.domain, user});
  }

  isShowSource(input) {
    const trimmedInput = input.trim();
    return Boolean(trimmedInput.includes("<dx_text_source>"))
  }

  streamText(index: number): void {
    if (this.listChatMessage[index].queue.length === 0) {
      this.isStreaming[index] = false; // Stop if queue is empty
      this.streamingQueue = this.streamingQueue.filter(number => number !== index);
      if (this.streamingQueue.length > 0 && !this.isStreaming[this.streamingQueue[0]]) {
        this.isStreaming[this.streamingQueue[0]] = true;
        this.streamText(this.streamingQueue[0]);
      }

      if (this.streamingQueue.length == 0 && !this.isStreaming[index]) {
        this.isSendingMessage = false;
        if (this.complexMessageQueue.length > 0) {
          const complexMessage = this.complexMessageQueue.shift();
          this.listChatMessage.push(complexMessage);
        }
      }
      return;
    }

    // Đảm bảo rằng message có thuộc tính pendingExclamation
    if (this.listChatMessage[index].pendingExclamation === undefined) {
      this.listChatMessage[index].pendingExclamation = false;
    }
    this.isSendingMessage = true;
    const message = this.listChatMessage[index];
    const currentText = this.extractUrls(message.queue.shift(), message.role); // Get the next chunk
    let charIndex = 0;
    let isInAnchorTag = false; // Flag to track if inside <a> tag

    // Biến để đánh dấu nếu ký tự cuối cùng của đoạn trước là !
    const isPendingExclamation = message.pendingExclamation === true;

    const addCharacter = () => {
      if (charIndex < currentText.length) {
        const currentChar = currentText[charIndex];

        // Kiểm tra nếu có dấu ! đang chờ xử lý từ đoạn trước
        if (!this.isProcessingImage && isPendingExclamation && charIndex === 0 && currentChar === '[') {
          this.isProcessingImage = true;
          this.imageMarkdownBuffer = '!'; // Thêm dấu ! vào buffer
          this.imageMarkdownBuffer += currentChar; // Thêm dấu [ vào buffer
          this.isLoadingImage = true;
          // Xóa trạng thái đánh dấu
          message.pendingExclamation = false;

          // Xóa dấu ! đã hiển thị ở cuối đoạn trước
          const currentContent = this.listChatMessage[index].content;
          if (currentContent.endsWith('!')) {
            this.zone.run(() => {
              this.listChatMessage[index].content = currentContent.substring(0, currentContent.length - 1);
            });
          }

          charIndex++;
          setTimeout(addCharacter, 10);
          return;
        }
        // Kiểm tra trường hợp bình thường: ! và [ cùng ở trong một đoạn
        else if (!this.isProcessingImage && currentChar === '!' && charIndex + 1 < currentText.length && currentText[charIndex + 1] === '[') {
          this.isProcessingImage = true;
          this.imageMarkdownBuffer = currentChar; // Start the buffer
          this.isLoadingImage = true;
          charIndex++;
          setTimeout(addCharacter, 20);
          return;
        }
        // Kiểm tra trường hợp ! ở cuối đoạn hiện tại
        else if (!this.isProcessingImage && currentChar === '!' && charIndex === currentText.length - 1) {
          // Đánh dấu để đoạn tiếp theo kiểm tra
          message.pendingExclamation = true;
          // Vẫn hiển thị dấu ! bình thường
          this.zone.run(() => {
            this.listChatMessage[index].content += currentChar;
          });
          charIndex++;
          setTimeout(addCharacter, 20);
          return;
        }
        else if (this.isProcessingImage) {
          this.imageMarkdownBuffer += currentChar;
          if (currentChar === ')') {
            // Markdown image syntax ends
            const imageMatch = this.imageMarkdownBuffer.match(/!\[(.*?)\]\((.*?)\)/);
            if (imageMatch) {
              const altText = imageMatch[1];
              const src = imageMatch[2];
              const imgTag = `<img id="imgCheck" class="rounded-xl zoom" src="${src}" alt="${altText}">`;

              this.zone.run(() => {
                this.listChatMessage[index].content += imgTag; // Add converted image tag
              });
            }
            this.isProcessingImage = false; // Reset flag
            this.imageMarkdownBuffer = ""; // Clear buffer
            this.isLoadingImage = false;
          }
          charIndex++;
          setTimeout(addCharacter, 20);
          return;
        }
        if (!this.isProcessingImage) {
          if (currentChar === '<' && currentText.substring(charIndex, charIndex + 3) === '<a ') {
            isInAnchorTag = true;
            this.zone.run(() => {
              this.listChatMessage[index].content += currentText.substring(charIndex, charIndex + 3); // Add <a>
            });
            charIndex += 3;
          } else if (isInAnchorTag && currentChar === '>' && currentText.substring(charIndex - 1, charIndex + 1) === '</a>') {
            this.zone.run(() => {
              this.listChatMessage[index].content += currentText[charIndex]; // Add </a>
            });
            isInAnchorTag = false;
            charIndex++;
          } else if (currentChar === '<' && currentText.substring(charIndex, charIndex + 4) === '<img') {
            // Khi gặp thẻ <img> trong text, đặt isLoadingImage = true
            this.isLoadingImage = true;
          }

          this.zone.run(() => {
            this.listChatMessage[index].content += currentChar;
          });
          if (currentChar === '>') {
            // Nếu đã hoàn thành thẻ <img>, đặt isLoadingImage = false
            setTimeout(() => {
              if (this.isLoadingImage)
                this.isLoadingImage = false;
            }, 0);
          }

          charIndex++;
          setTimeout(addCharacter, 20); // Adjust delay for streaming speed

          // Scroll to bottom during streaming if auto-scroll is enabled
          if (this.isAutoScrollDown) {
            // Sử dụng requestAnimationFrame để đảm bảo scroll diễn ra sau khi DOM đã được cập nhật
            requestAnimationFrame(() => {
              this.scrollToBottomOfNgScrollBar();
            });
          }
        }

      } else {
        // Nếu không còn đoạn nào nữa và có pendingExclamation, hiển thị nó như một ký tự bình thường
        if (message.queue.length === 0 && message.pendingExclamation) {
          this.zone.run(() => {
            this.listChatMessage[index].content += '!';
          });
          message.pendingExclamation = false;
        }

        this.streamText(index); // Process the next chunk
      }
    };

    addCharacter();
  }

  checkTheVoid(input) {
    const cleanedContent = input.replace(/^[\s\n]+/, '');
    return cleanedContent; // Trả về chuỗi đã được loại bỏ các ký tự ở đầu
  }

  private findTextAliasesInteractiveButton(message: string): any[] {
    const textAliases: any[] = [];

    if (this.listChatMessage.length > 0) {
      const lastMessage = this.listChatMessage[this.listChatMessage.length - 1];
      if (lastMessage.content && lastMessage.content.buttons && lastMessage.content.buttons.length > 0) {
        lastMessage.content.buttons.forEach(btn => {
          if (btn.text_aliases && Array.isArray(btn.text_aliases)) {
            btn.text_aliases.forEach(alias => {
              if (alias.toLowerCase().includes(message.toLowerCase())) {
                textAliases.push({
                  value: btn.value,
                  link: btn.link,
                  actionId: btn.action_id
                });
              }
            });
          }
        });
      }
    }

    return textAliases;
  }
}
