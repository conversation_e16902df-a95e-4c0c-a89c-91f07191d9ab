import {AfterViewInit, Component, ElementRef, ViewChild} from '@angular/core';
import * as go from 'gojs';

@Component({
  selector: 'app-diagram',
  templateUrl: './diagram.component.html',
  styleUrls: ['./diagram.component.scss']
})
export class DiagramComponent implements AfterViewInit{

  private myDiagram: any;

  ngAfterViewInit() {
    this.initDiagram();
  }

  initDiagram() {
    // Since 2.2 you can also author concise templates with method chaining instead of GraphObject.make
    // For details, see https://gojs.net/latest/intro/buildingObjects.html
    const $ = go.GraphObject.make;

    this.myDiagram = new go.Diagram('myDiagramDiv', {
      allowDelete: false,
      allowCopy: false,
      'undoManager.isEnabled': true
    });


    // the template for each attribute in a node's array of item data
    var itemTempl =
      $(go.Panel, "Horizontal",
        $(go.Shape,
          { desiredSize: new go.Size(15, 15), strokeJoin: "round", strokeWidth: 3, stroke: "#eeeeee", margin: 2 },
        ),

        $(go.TextBlock,
          {
            font: " 14px sans-serif",
            stroke: "black"
          },
          new go.Binding("text", "name"), new go.Binding("stroke",  "", n => (this.myDiagram.model.modelData.darkMode) ? "#f5f5f5" :"#000000")),
      );




    // define the Node template, representing an entity
   this.myDiagram.nodeTemplate =
      $(go.Node, "Auto",  // the whole node panel
        {
          selectionAdorned: true,
          resizable: true,
          layoutConditions: go.Part.LayoutStandard & ~go.Part.LayoutNodeSized,
          fromSpot: go.Spot.LeftRightSides,
          toSpot: go.Spot.LeftRightSides,
          isShadowed: true,
          shadowOffset: new go.Point(4, 4),
          shadowColor: "#919cab"
        },
        new go.Binding("location", "location").makeTwoWay(),
        // whenever the PanelExpanderButton changes the visible property of the "LIST" panel,
        // clear out any desiredSize set by the ResizingTool.
        new go.Binding("desiredSize", "visible", v => new go.Size(NaN, NaN)).ofObject("LIST"),
        // define the node's outer shape, which will surround the Table
        $(go.Shape, "RoundedRectangle",
          { stroke: "#e8f1ff", strokeWidth: 3 },
          new go.Binding("fill", "", n => (this.myDiagram.model.modelData.darkMode) ? "#4a4a4a" :"#f7f9fc")
        ),


        $(go.Panel, "Table",
          { margin: 8,
            stretch: go.GraphObject.Fill,
            width: 180
          },
          $(go.RowColumnDefinition, { row: 0, sizing: go.RowColumnDefinition.None }),
          // the table header
          $(go.TextBlock,
            {
              row: 0, alignment: go.Spot.Center,
              margin: new go.Margin(0, 24, 0, 2),  // leave room for Button
              font: "bold 16px sans-serif"
            },
            new go.Binding("text", "key"),
            new go.Binding("stroke",  "", n => (this.myDiagram.model.modelData.darkMode) ? "#d6d6d6" :"#000000")
          ),
          // the collapse/expand button
          $("PanelExpanderButton", "LIST",  // the name of the element whose visibility this button toggles
            { row: 0, alignment: go.Spot.TopRight },
            new go.Binding("ButtonIcon.stroke",  "", n => (this.myDiagram.model.modelData.darkMode) ? "#d6d6d6" :"#000000")
          ),
          // the list of Panels, each showing an attribute

          $(go.Panel, "Table",
            {
              name: "LIST",
              row: 1,
              padding: 3,
              alignment: go.Spot.TopLeft,
              defaultAlignment: go.Spot.Left,
              stretch: go.GraphObject.Horizontal,
              itemTemplate: itemTempl,
            }),
          $(go.TextBlock,
            {
              font: "bold 15px sans-serif",
              text: "Attributes",
              row: 1,
              alignment: go.Spot.TopLeft,
              margin: new go.Margin(8, 0, 0, 0),
            },
            new go.Binding("stroke",  "", n => (this.myDiagram.model.modelData.darkMode) ? "#d6d6d6" :"#000000")
          ),
          $("PanelExpanderButton", "NonInherited", // the name of the element whose visibility this button toggles
            { row: 1,
              column: 1
            },
            new go.Binding("ButtonIcon.stroke", "", n => (this.myDiagram.model.modelData.darkMode) ? "#d6d6d6" :"#000000")
          ),

          $(go.Panel, "Vertical",
            {
              name: "NonInherited",
              alignment: go.Spot.TopLeft,
              defaultAlignment: go.Spot.Left,
              itemTemplate: itemTempl,
              row: 2
            },
            new go.Binding("itemArray", "items")
          ),

          $(go.TextBlock,
            {
              font: "bold 15px sans-serif",
              text: "Foreign key",
              row: 3,
              alignment: go.Spot.TopLeft,
              margin: new go.Margin(8, 0, 0, 0),
            },
            new go.Binding("visible", "visibility", Boolean),
            new go.Binding("stroke",  "", n => (this.myDiagram.model.modelData.darkMode) ? "#d6d6d6" :"#000000")
          ),
          $("PanelExpanderButton", "Inherited" , // the name of the element whose visibility this button toggles
            {
              row: 3,
              column: 1,
            },
            new go.Binding("visible", "visibility", Boolean),
            new go.Binding("ButtonIcon.stroke",  "", n => (this.myDiagram.model.modelData.darkMode) ? "#d6d6d6" :"#000000")
          ),
          $(go.Panel, "Vertical",
            {
              name: "Inherited",
              alignment: go.Spot.TopLeft,
              defaultAlignment: go.Spot.Left,
              itemTemplate: itemTempl,
              row: 4
            },
            new go.Binding("itemArray", "inheriteditems")
          )
        )
        // end Table Panel
      );  // end Node

    // define the Link template, representing a relationship
    this.myDiagram.linkTemplate =
      $(go.Link,  // the whole link panel
        {
          selectionAdorned: true,
          layerName: "Background",
          reshapable: true,
          routing: go.Link.AvoidsNodes,
          corner: 5,
          curve: go.Link.JumpOver,
          isShadowed: true,
          shadowOffset: new go.Point(2, 2),
          shadowColor: "#919cab"
        },
        $(go.Shape,  // the link shape
          { stroke: "#f7f9fc", strokeWidth: 4 }),
        $(go.Panel, "Auto", {segmentIndex: 0 , segmentOffset: new go.Point(22,0)},
          $(go.Shape, "RoundedRectangle", {fill: "#f7f9fc"}, {stroke: "#eeeeee"}),
          $(go.TextBlock,  // the "from" label
            {
              textAlign: "center",
              font: "bold 14px sans-serif",
              stroke: "black",
              background: "#f7f9fc",
              segmentOffset: new go.Point(NaN, NaN),
              segmentOrientation: go.Link.OrientUpright
            },
            new go.Binding("text", "text"))),
        $(go.Panel, "Auto",
          {
            segmentIndex: -1,
            segmentOffset: new go.Point(-13,0)
          },
          $(go.Shape, "RoundedRectangle", {fill: "#edf6fc"}, {stroke: "#eeeeee"}),
          $(go.TextBlock,  // the "to" label
            {
              textAlign: "center",
              font: "bold 14px sans-serif",
              stroke: "black",
              segmentIndex: -1,
              segmentOffset: new go.Point(NaN, NaN),
              segmentOrientation: go.Link.OrientUpright
            },
            new go.Binding("text", "toText"))),

      );

    // create the model for the E-R diagram
    var nodeDataArray = [
      {
        key: "revenue_cost_static", visibility: true, location: new go.Point(400,100) ,
        items: [{ name: "id", iskey: true },
          { name: "entity", iskey: false},
          { name: "type", iskey: false },
          { name: "type_value", iskey: false}],
        inheriteditems:
          [{ name: "entity_id", iskey: true }]
      },

      {
        key: "revenue_cost_plan", visibility: true,location: new go.Point(400,400) ,
        items: [{ name: "id", iskey: true},
          { name: "entity", iskey: false},
          { name: "type", iskey: false },
          { name: "type_value", iskey: false},
          { name: "value", iskey: false }],
        inheriteditems: [
          { name: "entity_id", iskey: false }]
      },
      {
        key: "project", visibility: true,location: new go.Point(100,100) ,
        items: [{ name: "id", iskey: true},
          { name: "name", iskey: false},
          { name: "id_deleted", iskey: false}],
        inheriteditems: [
          { name: "partner_id", iskey: false},
          { name: "team_id", iskey: false}]
      },
      {
        key: "team", visibility: true,location: new go.Point(100,400) ,
        items: [{ name: "id", iskey: true},
          { name: "name", iskey: false},
          { name: "is_delete", iskey: false}],
        inheriteditems: [
          { name: "department_id", iskey: true}]
      },
      {
        key: "partner", visibility: true,location: new go.Point(700,100) ,
        items: [{ name: "id", iskey: true},
          { name: "name", iskey: false},
          { name: "id_delete", iskey: false}],
        inheriteditems: []
      },
      {
        key: "department", visibility: true,location: new go.Point(700,400) ,
        items: [{ name: "id", iskey: true},
          { name: "name", iskey: false},
          { name: "is_deleted", iskey: false}],
        inheriteditems: [{ name: "ProductID", iskey: true, figure: "Decision", color: "purple" }]
      },
    ];
    var linkDataArray = [
      { from: "revenue_cost_static", to: "project", text: "0..N", toText: "1" },
      { from: "revenue_cost_plan", to: "project", text: "0..N", toText: "1" },
      { from: "project", to: "partner", text: "0..N", toText: "1" },
      { from: "project", to: "team", text: "0..N", toText: "1" },
      { from: "team", to: "department", text: "1", toText: "1" },
      { from: "revenue_cost_static", to: "team", text: "0..N", toText: "1" },
      { from: "revenue_cost_static", to: "partner", text: "0..N", toText: "1" },
      { from: "revenue_cost_static", to: "department", text: "0..N", toText: "1" },
      { from: "revenue_cost_plan", to: "team", text: "0..N", toText: "1" },
      { from: "revenue_cost_plan", to: "partner", text: "0..N", toText: "1" },
      { from: "revenue_cost_plan", to: "department", text: "0..N", toText: "1" },
    ];
    this.myDiagram.model = new go.GraphLinksModel(
      {
        copiesArrays: true,
        copiesArrayObjects: true,
        nodeDataArray: nodeDataArray,
        linkDataArray: linkDataArray
      });
    this.myDiagram.model.modelData.darkMode = false;
  }

}
