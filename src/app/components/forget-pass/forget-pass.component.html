<div class="bg-[#010314] px-12 h-screen max-w-screen flex justify-center items-center">
  <div
    class="relative px-12  py-12 md:px-[88px] w-full md:w-[616px] bg-[#010314] rounded-[42px] border-[#664DFF] border-t border-b-[3px] shadow-gradient">
    <div class="text-white">
      <svg width="189" height="50" viewBox="0 0 189 50" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.1922 22.7076H12.4854V31.2085C11.1232 30.378 9.99053 29.2138 9.1922 27.8237V22.7076ZM38.367 40.2735C38.5163 40.4263 38.6001 40.6325 38.6001 40.8473C38.6001 41.0621 38.5163 41.2683 38.367 41.4212L35.613 44.2029C35.4617 44.3538 35.2576 44.4384 35.0449 44.4384C34.8323 44.4384 34.6282 44.3538 34.4768 44.2029L27.6593 37.3166C27.6555 37.3129 27.6518 37.3092 27.6481 37.3053C27.616 37.2729 27.6032 37.2323 27.5791 37.1983C24.6278 39.3747 21.068 40.547 17.4139 40.5458C7.84251 40.5458 0.0830078 32.708 0.0830078 23.0383C0.0830078 13.3687 7.84251 5.53247 17.4155 5.53247C26.9885 5.53247 34.7464 13.3703 34.7464 23.0399C34.7464 26.8802 33.5075 30.4206 31.4308 33.3061C31.4709 33.3337 31.5142 33.3515 31.5495 33.3872L38.367 40.2735ZM30.2977 23.0383C30.2936 20.4704 29.5362 17.9613 28.1212 15.8278C26.7063 13.6944 24.6971 12.0324 22.3476 11.0516C19.998 10.0708 17.4135 9.81528 14.9202 10.3173C12.427 10.8193 10.137 12.0563 8.3393 13.8721C6.54164 15.6879 5.31698 18.0011 4.82 20.5195C4.32301 23.0379 4.57598 25.6485 5.54697 28.0218C6.51794 30.3951 8.16339 32.4245 10.2755 33.8537C12.3876 35.283 14.8716 36.048 17.4139 36.0522H17.4524C19.1418 36.0497 20.8142 35.711 22.374 35.0556C23.9338 34.4002 25.3506 33.4409 26.5434 32.2325C27.7362 31.024 28.6817 29.5901 29.3258 28.0126C29.97 26.435 30.3003 24.7448 30.2977 23.0383ZM22.4981 31.1112C23.8675 30.2288 24.9937 29.0109 25.772 27.5708V24.7113H22.4981V31.1112ZM15.2762 32.648C15.9856 32.7404 16.6997 32.7777 17.4139 32.7647C18.0558 32.8052 18.6978 32.8052 19.3397 32.7647V18.0017H15.2778V32.648H15.2762Z" fill="url(#paint0_linear_325_2801)"/>
        <path d="M189 15.531V19.2473H182.944V38.4863H178.386V19.2473H172.33V15.531H189Z" fill="url(#paint1_linear_325_2801)"/>
        <path d="M169.647 22.6347C169.647 23.8625 169.354 25.0135 168.768 26.0878C168.204 27.1622 167.303 28.0282 166.066 28.6859C164.85 29.3437 163.309 29.6725 161.442 29.6725H157.633V38.4863H153.075V15.531H161.442C163.2 15.531 164.698 15.838 165.935 16.4519C167.173 17.0657 168.095 17.9099 168.703 18.9842C169.332 20.0585 169.647 21.2753 169.647 22.6347ZM161.247 25.9563C162.506 25.9563 163.439 25.6713 164.047 25.1012C164.655 24.5092 164.959 23.6871 164.959 22.6347C164.959 20.3983 163.721 19.2802 161.247 19.2802H157.633V25.9563H161.247Z" fill="url(#paint2_linear_325_2801)"/>
        <path d="M143.256 22.4374C142.735 21.4727 142.018 20.7382 141.107 20.234C140.195 19.7297 139.132 19.4776 137.916 19.4776C136.57 19.4776 135.376 19.7845 134.335 20.3984C133.293 21.0123 132.479 21.8893 131.893 23.0294C131.307 24.1695 131.014 25.485 131.014 26.9759C131.014 28.5106 131.307 29.848 131.893 30.9881C132.5 32.1282 133.336 33.0052 134.4 33.6191C135.463 34.233 136.701 34.54 138.111 34.54C139.848 34.54 141.27 34.0795 142.377 33.1587C143.484 32.2159 144.211 30.9114 144.558 29.2451H136.744V25.7262H149.051V29.7384C148.747 31.3389 148.096 32.8189 147.098 34.1782C146.099 35.5375 144.808 36.6338 143.223 37.4669C141.66 38.2781 139.902 38.6838 137.949 38.6838C135.756 38.6838 133.77 38.1904 131.99 37.2038C130.232 36.1953 128.843 34.8031 127.823 33.0271C126.824 31.2512 126.325 29.2341 126.325 26.9759C126.325 24.7176 126.824 22.7005 127.823 20.9246C128.843 19.1268 130.232 17.7345 131.99 16.7479C133.77 15.7394 135.745 15.2351 137.916 15.2351C140.477 15.2351 142.702 15.8709 144.591 17.1426C146.479 18.3923 147.781 20.1572 148.498 22.4374H143.256Z" fill="url(#paint3_linear_325_2801)"/>
        <path d="M123.871 17.3748L111.285 48.426H107.699L111.819 38.2554L103.39 17.3748H107.242L113.802 34.4799L120.286 17.3748H123.871Z" fill="white"/>
        <path d="M94.3487 20.8032C94.9589 19.5961 95.8234 18.6587 96.9422 17.9909C98.0864 17.3231 99.4722 16.9893 101.099 16.9893V20.6106H100.184C96.2938 20.6106 94.3487 22.7423 94.3487 27.0058V38.4863H90.8779V17.3745H94.3487V20.8032Z" fill="white"/>
        <path d="M85.5338 27.1214C85.5338 27.7892 85.4957 28.4955 85.4194 29.2403H68.714C68.8412 31.3207 69.5404 32.9516 70.8117 34.133C72.1085 35.2887 73.6722 35.8666 75.503 35.8666C77.0031 35.8666 78.249 35.5199 79.2407 34.8264C80.2578 34.1073 80.9697 33.157 81.3765 31.9756H85.1143C84.5549 34.0046 83.4361 35.6612 81.7579 36.9453C80.0798 38.2038 77.9948 38.8331 75.503 38.8331C73.5197 38.8331 71.7398 38.3836 70.1633 37.4847C68.6123 36.5858 67.3918 35.3144 66.5019 33.6707C65.612 32.0013 65.167 30.075 65.167 27.8919C65.167 25.7088 65.5992 23.7954 66.4638 22.1517C67.3283 20.5079 68.536 19.2494 70.0871 18.3762C71.6635 17.4773 73.4688 17.0278 75.503 17.0278C77.4862 17.0278 79.2407 17.4645 80.7663 18.3377C82.2919 19.2109 83.4615 20.418 84.2752 21.9591C85.1143 23.4744 85.5338 25.1952 85.5338 27.1214ZM81.9486 26.3894C81.9486 25.0539 81.6562 23.911 81.0714 22.9607C80.4866 21.9847 79.6857 21.2528 78.6686 20.7648C77.6769 20.2511 76.5709 19.9943 75.3504 19.9943C73.596 19.9943 72.0958 20.5593 70.8499 21.6894C69.6294 22.8194 68.9301 24.3861 68.7522 26.3894H81.9486Z" fill="white"/>
        <path d="M59.8406 17.3748V38.4865H56.3698V35.366C55.7087 36.4447 54.7806 37.2922 53.5856 37.9087C52.416 38.4994 51.1192 38.7947 49.6953 38.7947C48.068 38.7947 46.6059 38.4608 45.3092 37.7931C44.0124 37.0996 42.9826 36.0723 42.2198 34.7111C41.4825 33.3498 41.1138 31.6933 41.1138 29.7413V17.3748H44.5464V29.279C44.5464 31.3594 45.0676 32.9646 46.1101 34.0947C47.1526 35.1991 48.5765 35.7512 50.3818 35.7512C52.238 35.7512 53.7 35.1734 54.7679 34.0176C55.8359 32.8619 56.3698 31.1796 56.3698 28.9708V17.3748H59.8406Z" fill="white"/>
        <defs>
          <linearGradient id="paint0_linear_325_2801" x1="19.5322" y1="44.3235" x2="17.994" y2="4.94456" gradientUnits="userSpaceOnUse">
            <stop stop-color="#5599FF"/>
            <stop offset="0.836794" stop-color="#7241FF"/>
            <stop offset="1" stop-color="#8E66FF"/>
          </linearGradient>
          <linearGradient id="paint1_linear_325_2801" x1="140.031" y1="10.9108" x2="195.006" y2="29.1329" gradientUnits="userSpaceOnUse">
            <stop offset="0.0695937" stop-color="#4467FF"/>
            <stop offset="1" stop-color="#7241FF"/>
          </linearGradient>
          <linearGradient id="paint2_linear_325_2801" x1="140.031" y1="10.9108" x2="195.006" y2="29.1329" gradientUnits="userSpaceOnUse">
            <stop offset="0.0695937" stop-color="#4467FF"/>
            <stop offset="1" stop-color="#7241FF"/>
          </linearGradient>
          <linearGradient id="paint3_linear_325_2801" x1="140.031" y1="10.9109" x2="195.006" y2="29.133" gradientUnits="userSpaceOnUse">
            <stop offset="0.0695937" stop-color="#4467FF"/>
            <stop offset="1" stop-color="#7241FF"/>
          </linearGradient>
        </defs>
      </svg>
      <div class="mt-[36px]">
        <h1 class="text-4xl md:text-5xl font-bold	mb-3">Forgot password?</h1>
        <h2 class="text-xl	font-normal	mb-9">Fill the form to reset your password</h2>
        <mat-form-field class="w-full mb-2">
          <svg matPrefix width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5 5.99634H19C19.3862 5.99634 19.7213 6.21531 19.8879 6.53588L12.5547 11.4247C12.2188 11.6486 11.7812 11.6486 11.4453 11.4247L4.11209 6.53586C4.27868 6.2153 4.61377 5.99634 5 5.99634ZM4 8.86484V16.9963C4 17.5486 4.44772 17.9963 5 17.9963H19C19.5523 17.9963 20 17.5486 20 16.9963V8.86485L13.6641 13.0888C12.6564 13.7606 11.3436 13.7606 10.3359 13.0888L4 8.86484ZM2 6.99634C2 5.33948 3.34315 3.99634 5 3.99634H19C20.6569 3.99634 22 5.33948 22 6.99634V16.9963C22 18.6532 20.6569 19.9963 19 19.9963H5C3.34315 19.9963 2 18.6532 2 16.9963V6.99634Z" fill="#EFEFEF"/>
          </svg>
          <input class="text-input-signin	custom-input" type="text" matInput placeholder="Email address *">
        </mat-form-field>
        <button class="w-full bg-[#7241FF] rounded-full py-3 mb-3">Send reset link</button>
        <p>Return to <span class="text-[#7241FF] cursor-pointer" routerLink="/auth/sign-in">sign in</span></p>
      </div>
    </div>
  </div>
</div>
