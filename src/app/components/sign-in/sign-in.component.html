<div class="bg-[#010314] h-screen max-w-screen flex justify-center items-center px-12">
<div class="relative px-12 md:px-0 w-full md:w-[700px] xl:w-[1050px] h-[75vh] bg-[#010314] rounded-[42px] border-[#664DFF] border-t border-b-[3px] shadow-gradient flex">
  <svg class="absolute hidden xl:block top-[59px] left-[53px]" width="158" height="41" viewBox="0 0 158 41" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.84128 18.211H10.5914V25.3101C9.45388 24.6166 8.50797 23.6444 7.84128 22.4835V18.211ZM32.2053 32.8803C32.33 33.008 32.4 33.1802 32.4 33.3596C32.4 33.5389 32.33 33.7111 32.2053 33.8388L29.9055 36.1618C29.7791 36.2878 29.6086 36.3585 29.431 36.3585C29.2534 36.3585 29.083 36.2878 28.9566 36.1618L23.2633 30.4111C23.2601 30.408 23.257 30.4048 23.2539 30.4016C23.2271 30.3745 23.2164 30.3407 23.1963 30.3123C20.7317 32.1298 17.7588 33.1088 14.7073 33.1078C6.71414 33.1078 0.234131 26.5623 0.234131 18.4872C0.234131 10.412 6.71414 3.86792 14.7086 3.86792C22.7031 3.86792 29.1818 10.4133 29.1818 18.4885C29.1818 21.6956 28.1471 24.6522 26.4128 27.0619C26.4463 27.0849 26.4825 27.0998 26.512 27.1296L32.2053 32.8803ZM25.4666 18.4872C25.4632 16.3427 24.8307 14.2473 23.649 12.4657C22.4674 10.684 20.7895 9.29603 18.8274 8.47697C16.8653 7.65791 14.7069 7.44452 12.6248 7.86375C10.5427 8.28297 8.63025 9.31601 7.12901 10.8324C5.62777 12.3488 4.60506 14.2805 4.19002 16.3837C3.77499 18.4868 3.98624 20.667 4.79712 22.6489C5.60799 24.6308 6.98211 26.3256 8.74594 27.5192C10.5098 28.7128 12.5842 29.3517 14.7073 29.3551H14.7394C16.1503 29.353 17.5469 29.0702 18.8495 28.5229C20.1521 27.9756 21.3353 27.1745 22.3314 26.1653C23.3275 25.1561 24.1171 23.9586 24.655 22.6412C25.193 21.3238 25.4688 19.9122 25.4666 18.4872ZM18.9531 25.2289C20.0967 24.492 21.0372 23.4749 21.6872 22.2723V19.8842H18.9531V25.2289ZM12.9221 26.5123C13.5145 26.5894 14.1109 26.6206 14.7073 26.6097C15.2434 26.6436 15.7795 26.6436 16.3155 26.6097V14.281H12.9234V26.5123H12.9221Z" fill="url(#paint0_linear_325_2625)"/>
    <path d="M158 12.2175V15.321H152.943V31.3877H149.136V15.321H144.079V12.2175H158Z" fill="url(#paint1_linear_325_2625)"/>
    <path d="M141.838 18.1498C141.838 19.1752 141.593 20.1364 141.104 21.0336C140.633 21.9308 139.881 22.654 138.847 23.2033C137.832 23.7526 136.545 24.0272 134.986 24.0272H131.805V31.3877H127.999V12.2175H134.986C136.455 12.2175 137.705 12.4739 138.739 12.9865C139.772 13.4992 140.542 14.2041 141.05 15.1013C141.575 15.9985 141.838 17.0146 141.838 18.1498ZM134.823 20.9237C135.875 20.9237 136.654 20.6857 137.162 20.2097C137.669 19.7153 137.923 19.0287 137.923 18.1498C137.923 16.2823 136.89 15.3485 134.823 15.3485H131.805V20.9237H134.823Z" fill="url(#paint2_linear_325_2625)"/>
    <path d="M119.799 17.9852C119.364 17.1795 118.765 16.5662 118.004 16.145C117.243 15.7239 116.355 15.5134 115.34 15.5134C114.216 15.5134 113.219 15.7697 112.349 16.2824C111.479 16.795 110.799 17.5274 110.309 18.4795C109.82 19.4316 109.575 20.5302 109.575 21.7752C109.575 23.0569 109.82 24.1738 110.309 25.1259C110.817 26.078 111.515 26.8104 112.403 27.3231C113.291 27.8357 114.324 28.0921 115.503 28.0921C116.953 28.0921 118.14 27.7076 119.065 26.9386C119.989 26.1512 120.596 25.0618 120.886 23.6703H114.361V20.7316H124.638V24.0823C124.385 25.4189 123.841 26.6548 123.007 27.79C122.173 28.9251 121.095 29.8406 119.771 30.5364C118.466 31.2138 116.998 31.5526 115.367 31.5526C113.536 31.5526 111.877 31.1406 110.391 30.3167C108.923 29.4744 107.763 28.3118 106.911 26.8287C106.077 25.3456 105.66 23.6611 105.66 21.7752C105.66 19.8894 106.077 18.2049 106.911 16.7218C107.763 15.2204 108.923 14.0578 110.391 13.2338C111.877 12.3916 113.527 11.9705 115.34 11.9705C117.478 11.9705 119.336 12.5014 120.913 13.5634C122.49 14.607 123.578 16.081 124.176 17.9852H119.799Z" fill="url(#paint3_linear_325_2625)"/>
    <path d="M103.61 13.7573L93.0993 39.6884H90.1053L93.5452 31.1949L86.5061 13.7573H89.7231L95.2014 28.042L100.616 13.7573H103.61Z" fill="white"/>
    <path d="M78.9558 16.6206C79.4654 15.6126 80.1874 14.8297 81.1217 14.272C82.0772 13.7144 83.2345 13.4355 84.5935 13.4355V16.4598H83.829C80.5802 16.4598 78.9558 18.24 78.9558 21.8004V31.3878H76.0574V13.7573H78.9558V16.6206Z" fill="white"/>
    <path d="M71.5946 21.8967C71.5946 22.4544 71.5628 23.0442 71.4991 23.6662H57.5483C57.6545 25.4036 58.2384 26.7655 59.3001 27.7522C60.3831 28.7173 61.689 29.1999 63.2178 29.1999C64.4706 29.1999 65.5111 28.9104 66.3392 28.3313C67.1886 27.7307 67.7831 26.9371 68.1229 25.9505H71.2443C70.7771 27.6449 69.8428 29.0283 68.4414 30.1008C67.0399 31.1517 65.2987 31.6772 63.2178 31.6772C61.5616 31.6772 60.0752 31.3019 58.7587 30.5512C57.4634 29.8005 56.4442 28.7388 55.701 27.3661C54.9578 25.9719 54.5862 24.3633 54.5862 22.5402C54.5862 20.7171 54.9472 19.1192 55.6691 17.7465C56.3911 16.3738 57.3997 15.3228 58.695 14.5936C60.0115 13.8429 61.5191 13.4675 63.2178 13.4675C64.8741 13.4675 66.3392 13.8322 67.6133 14.5614C68.8873 15.2906 69.8641 16.2987 70.5436 17.5856C71.2443 18.8511 71.5946 20.2881 71.5946 21.8967ZM68.6006 21.2855C68.6006 20.1702 68.3564 19.2157 67.8681 18.4221C67.3797 17.6071 66.7108 16.9958 65.8615 16.5883C65.0333 16.1593 64.1096 15.9448 63.0904 15.9448C61.6253 15.9448 60.3725 16.4167 59.332 17.3604C58.3128 18.3041 57.7288 19.6125 57.5802 21.2855H68.6006Z" fill="white"/>
    <path d="M50.1381 13.7573V31.3879H47.2397V28.7819C46.6876 29.6828 45.9126 30.3905 44.9146 30.9053C43.9378 31.3986 42.8549 31.6453 41.6658 31.6453C40.3068 31.6453 39.0858 31.3665 38.0029 30.8088C36.9199 30.2297 36.06 29.3718 35.4229 28.235C34.8072 27.0982 34.4993 25.7148 34.4993 24.0847V13.7573H37.3659V23.6987C37.3659 25.436 37.8012 26.7765 38.6718 27.7202C39.5423 28.6425 40.7315 29.1036 42.2391 29.1036C43.7892 29.1036 45.0101 28.6211 45.9019 27.6559C46.7938 26.6907 47.2397 25.2858 47.2397 23.4413V13.7573H50.1381Z" fill="white"/>
    <defs>
      <linearGradient id="paint0_linear_325_2625" x1="16.4763" y1="36.2625" x2="15.1917" y2="3.37695" gradientUnits="userSpaceOnUse">
        <stop stop-color="#5599FF"/>
        <stop offset="0.836794" stop-color="#7241FF"/>
        <stop offset="1" stop-color="#8E66FF"/>
      </linearGradient>
      <linearGradient id="paint1_linear_325_2625" x1="117.106" y1="8.35916" x2="163.016" y2="23.5765" gradientUnits="userSpaceOnUse">
        <stop offset="0.0695937" stop-color="#4467FF"/>
        <stop offset="1" stop-color="#7241FF"/>
      </linearGradient>
      <linearGradient id="paint2_linear_325_2625" x1="117.106" y1="8.35916" x2="163.016" y2="23.5765" gradientUnits="userSpaceOnUse">
        <stop offset="0.0695937" stop-color="#4467FF"/>
        <stop offset="1" stop-color="#7241FF"/>
      </linearGradient>
      <linearGradient id="paint3_linear_325_2625" x1="117.106" y1="8.35928" x2="163.016" y2="23.5766" gradientUnits="userSpaceOnUse">
        <stop offset="0.0695937" stop-color="#4467FF"/>
        <stop offset="1" stop-color="#7241FF"/>
      </linearGradient>
    </defs>
  </svg>
  <div class="h-full hidden xl:block">
    <img src="assets/img/login-pic.png" class="h-full ">
  </div>
  <div class="flex justify-center items-center text-white w-full xl:w-2/3 ">
    <div class="w-[396px]" >
      <div class="mb-10">
        <h1 class="text-5xl	font-semibold mb-3	">Sign In</h1>
        <h5 class="text-xl	font-normal">Don't have an account? <span class="cursor-pointer underline underline-offset-2	text-[#7241FF]">Sign up</span></h5>
      </div>
      <div>
        <mat-form-field class="w-full mb-2">
          <svg matPrefix class="mr-2" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M12 10C13.6569 10 15 8.65685 15 7C15 5.34315 13.6569 4 12 4C10.3431 4 9 5.34315 9 7C9 8.65685 10.3431 10 12 10ZM12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="#EFEFEF"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M9 16C6.79086 16 5 17.7909 5 20V21C5 21.5523 4.55228 22 4 22C3.44772 22 3 21.5523 3 21V20C3 16.6863 5.68629 14 9 14H15C18.3137 14 21 16.6863 21 20V21C21 21.5523 20.5523 22 20 22C19.4477 22 19 21.5523 19 21V20C19 17.7909 17.2091 16 15 16H9Z" fill="#EFEFEF"/>
          </svg>
          <input [(ngModel)]="account" class="text-input-signin !text-white" type="text" matInput placeholder="Username" (keyup.enter)="checkSignIn()">
        </mat-form-field>
        <mat-form-field class="w-full">
          <svg matPrefix class="mr-2" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13 15.7324C13.5978 15.3866 14 14.7403 14 14C14 12.8954 13.1046 12 12 12C10.8954 12 10 12.8954 10 14C10 14.7403 10.4022 15.3866 11 15.7324V17C11 17.5523 11.4477 18 12 18C12.5523 18 13 17.5523 13 17V15.7324Z" fill="#EFEFEF"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M7 8V7C7 4.23858 9.23858 2 12 2C14.7614 2 17 4.23858 17 7V8C18.6569 8 20 9.34315 20 11V19C20 20.6569 18.6569 22 17 22H7C5.34315 22 4 20.6569 4 19V11C4 9.34315 5.34315 8 7 8ZM9 7C9 5.34315 10.3431 4 12 4C13.6569 4 15 5.34315 15 7V8H9V7ZM6 11C6 10.4477 6.44772 10 7 10H17C17.5523 10 18 10.4477 18 11V19C18 19.5523 17.5523 20 17 20H7C6.44772 20 6 19.5523 6 19V11Z" fill="#EFEFEF"/>
          </svg>
          <input [(ngModel)]="password" class="text-input-signin !text-white" [type]="showPassword ? 'text' : 'password'" matInput placeholder="Password" (keyup.enter)="checkSignIn()">

          <svg matSuffix  *ngIf="!showPassword" (click)="togglePasswordVisibility()" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M12.0001 17C16.1121 17 18.5866 13.933 19.6831 12.1047C19.7081 12.063 19.715 12.0278 19.715 12C19.715 11.9722 19.7081 11.937 19.6831 11.8953C18.5866 10.067 16.1121 7 12.0001 7C7.88802 7 5.41349 10.067 4.31706 11.8953C4.29201 11.937 4.28516 11.9722 4.28516 12C4.28516 12.0278 4.29201 12.063 4.31706 12.1047C5.41349 13.933 7.88801 17 12.0001 17ZM21.3983 13.1334C20.21 15.1148 17.2151 19 12.0001 19C6.78504 19 3.79014 15.1148 2.60186 13.1334C2.17959 12.4292 2.17959 11.5708 2.60186 10.8666C3.79014 8.88521 6.78504 5 12.0001 5C17.2151 5 20.21 8.88521 21.3983 10.8666C21.8205 11.5708 21.8205 12.4292 21.3983 13.1334Z" fill="#EFEFEF"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13ZM12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" fill="#EFEFEF"/>
          </svg>

          <svg matSuffix *ngIf="showPassword" (click)="togglePasswordVisibility()" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M10.7077 9.29187C9.69768 9.7747 9 10.8059 9 12C9 13.6569 10.3431 15 12 15C13.1941 15 14.2253 14.3023 14.7081 13.2924L10.7077 9.29187ZM13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12Z" fill="#6F767E"/>
            <path d="M7.50097 6.08517C4.98543 7.38362 3.39307 9.54721 2.6018 10.8666C2.17953 11.5708 2.17953 12.4292 2.60179 13.1334C3.79008 15.1148 6.78498 19 12 19C14.6127 19 16.6681 18.0249 18.2183 16.8025L16.7918 15.376C15.5478 16.3033 13.9599 17 12 17C7.88795 17 5.41343 13.933 4.317 12.1047C4.29195 12.063 4.2851 12.0278 4.2851 12C4.2851 11.9722 4.29195 11.937 4.317 11.8953C5.11577 10.5633 6.64593 8.57399 9.0078 7.592L7.50097 6.08517Z" fill="#EFEFEF"/>
            <path d="M18.2677 14.0235C18.8756 13.3503 19.3449 12.6686 19.683 12.1047C19.7081 12.063 19.7149 12.0278 19.7149 12C19.7149 11.9722 19.7081 11.937 19.683 11.8953C18.5866 10.067 16.1121 7 12 7C11.7529 7 11.5118 7.01107 11.2765 7.03228L9.54817 5.30394C10.3036 5.11058 11.1202 5 12 5C17.215 5 20.2099 8.88521 21.3982 10.8666C21.8205 11.5708 21.8205 12.4292 21.3982 13.1334C21.0169 13.7693 20.4494 14.6013 19.6822 15.438L18.2677 14.0235Z" fill="#EFEFEF"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M2.29289 2.29289C2.68342 1.90237 3.31658 1.90237 3.70711 2.29289L21.7071 20.2929C22.0976 20.6834 22.0976 21.3166 21.7071 21.7071C21.3166 22.0976 20.6834 22.0976 20.2929 21.7071L2.29289 3.70711C1.90237 3.31658 1.90237 2.68342 2.29289 2.29289Z" fill="#EFEFEF"/>
          </svg>

        </mat-form-field>
        <div *ngIf="deniedAccess" class="text-red-600">Wrong account or password. Try again!</div>
        <div class="md:flex md:justify-between items-center mb-3" >
          <section>
            <mat-checkbox [(ngModel)]="isRemember" ><span class="!text-white text-sm">Remember me</span></mat-checkbox>
          </section>
          <p class="text-sm	text-[#7241FF] cursor-pointer"  routerLink="/auth/forget-password">Forgot password?</p>
        </div>
        <p class="text-xs text-[#6F767E] font-normal	mb-[36px]">By Signing in, you agree to queryGPT <span class="underline underline-offset-2 cursor-pointer">term of service privacy policy</span> and <span class="underline underline-offset-2 cursor-pointer">data usage properties</span></p>
        <button class="w-full bg-[#7241FF] rounded-full py-3" (click)="checkSignIn()">Sign In</button>
      </div>
    </div>
  </div>
</div>
</div>
