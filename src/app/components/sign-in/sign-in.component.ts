import {Component} from '@angular/core';
import {LogInService} from "../../shared/services/log-in.service";
import {Router} from "@angular/router";

@Component({
  selector: 'app-sign-in',
  templateUrl: './sign-in.component.html',
  styleUrls: ['./sign-in.component.scss']
})
export class SignInComponent {
  account = '';
  password = '';

  showPassword: boolean = false;
  isRemember: boolean = false;
  deniedAccess: boolean = false;

  constructor(private logInService: LogInService,
              private router: Router) {
    localStorage.clear();
    sessionStorage.clear();
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  checkSignIn() {
    this.logInService.signIn({login: this.account, password: this.password}).subscribe(
      (res) => {
        if (res.success) {
          this.logInService.sendLoginName(this.account);
          this.deniedAccess = false;
          this.router.navigate(['/query-gpt/chat']);
          if (this.isRemember) {
            localStorage.setItem('accessToken', res.token);
          } else {
            sessionStorage.setItem('accessToken', res.token);
          }
          const {fullName, login, position} = res.user;
          this.logInService.updateUserInfoInStorage({fullName, login, position}, this.isRemember);
        }
      },
      (error) => {
        if (error.status === 401) {
          // Xử lý khi thông tin đăng nhập không chính xác
          console.log('Unauthorized: Invalid credentials');
          this.deniedAccess = true;
        } else {
          // Xử lý các lỗi khác
          console.error('Unexpected error:', error);
        }
      }
    );
  }
}
