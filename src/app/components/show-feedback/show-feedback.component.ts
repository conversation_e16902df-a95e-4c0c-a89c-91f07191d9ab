import {Component, Injector, OnInit, ViewChild} from '@angular/core';
import {FeedbackService} from "../../shared/services/feedback.service";
import {BaseComponent} from "@core/base.component";
import {MatSnackBar} from "@angular/material/snack-bar";

@Component({
  selector: 'app-show-feedback',
  templateUrl: './show-feedback.component.html',
  styleUrls: ['./show-feedback.component.scss']
})
export class ShowFeedbackComponent extends BaseComponent implements OnInit {
  @ViewChild('confirm') confirm: any;

  idFeedback: number;
  listFeedback = [];
  // displayedColumns: any = ['question', 'rating', 'status', 'text'];
  columns: any[] = [{
    columnDef: 'dateAgo',
    width: "100px",
    headerName: "Date ago"
  }, {
    columnDef: 'question',
    width: "300px",
    headerName: "Question"
  }, {
    columnDef: 'rating',
    width: "150px",
    headerName: "Rating"
  }, {
    columnDef: 'status',
    width: "150px",
    headerName: "Status"
  }, {
    columnDef: 'reason',
    headerName: "Reason"
  }, {
    columnDef: 'action',
    width: "150px",
    headerName:  "Action"
  }];

  filter: any = {
    rating: '',
    status: '',
    page: 1,
    limit: 10,
  };

  listRating = [
    {
      label: 'all',
      value: ''
    },
    {
      label: 'like',
      value: 'thumb up'
    },
    {
      label: 'dislike',
      value: 'thumb down'
    }
  ];

  listStatus = [
    {
      label: 'all',
      value: ''
    },
    {
      label: 'new',
      value: 'NEW'
    },
    {
      label: 'resolved',
      value: 'RESOLVED'
    }
  ];

  limit: any = 10;
  count: any = 100;


  get displayedColumns(): any {
    return this.columns.map((c) => c.columnDef);
  }

  constructor(private showFeedbackService: FeedbackService,
              injector: Injector) {
    super(injector)
  }

  ngOnInit() {
    this.callListFeedback();
  }

  callListFeedback() {
    this.showFeedbackService.getListFeedback(this.filter).subscribe(res => {
      if (res.success) {
        this.listFeedback = res.feedback;
        this.count = res.totalCount
      }
    })
  }

  confirmFeedback(id: number) {
    this.idFeedback = id;
    this.showDialog(this.confirm, {
      data: {},
      width: '30vw',
    },)
  }

  resolvedFeedback() {
    this.showFeedbackService.resolvedFeedback(this.idFeedback).subscribe(res => {
      if(res.success){
        this.callListFeedback();
        this.showSnackBar('success', 'success');
      } else {
        this.showSnackBar('sth went wrong', );
      }

    })

  }

  changePage(emit: any) {
    this.filter.page = emit.pageIndex + 1;
    this.filter.limit = emit.pageSize;
    this.callListFeedback();
  }
}
