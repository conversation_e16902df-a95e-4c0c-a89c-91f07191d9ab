<div class="w-full h-full">
  <div class="p-8">
    <div class="header flex gap-8 text-white">
      <div>
        <mat-label>Rating</mat-label>
        <app-search-input class="w-40 filterSelect"
                          [setOptionValue]="'value'"
                          [setOptionLabel]="'label'"
                          [value]="filter.rating"
                          (valueChange)="filter.rating = $event ;  callListFeedback()"
                          [listOptions]="listRating"
                          [setCanBeSearch]="false"
                          [haveTooltip]="false"
        >
        </app-search-input>
      </div>
      <div>
        <mat-label>Status</mat-label>
        <app-search-input class="w-40 filterSelect"
                          [setOptionValue]="'value'"
                          [setOptionLabel]="'label'"
                          [value]="filter.status"
                          (valueChange)="filter.status = $event ;  callListFeedback()"
                          [listOptions]="listStatus"
                          [setCanBeSearch]="false"
                          [haveTooltip]="false"
        >
        </app-search-input>
      </div>
    </div>
    <div class="max-h-[70vh] overflow-auto">
      <table *ngIf="listFeedback.length > 0" mat-table [dataSource]="listFeedback" class="no-shadow w-full">
        <ng-container *ngFor='let column of columns'
                      [matColumnDef]='column.columnDef'>
          <th mat-header-cell *matHeaderCellDef class="header-table"
              [ngStyle]="{'width':column.width}"
          > {{ column.headerName }}</th>
          <td mat-cell *matCellDef="let element">
            <ng-container [ngSwitch]='column.columnDef'>
              <ng-container *ngSwitchCase="'action'">
                <div *ngIf="element.status !=='RESOLVED'" (click)="confirmFeedback(element.id)" class="flex justify-center cursor-pointer">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M20.7071 6.29289C21.0976 6.68342 21.0976 7.31658 20.7071 7.70711L12.1213 16.2929C10.9497 17.4645 9.05026 17.4645 7.87868 16.2929L4.29289 12.7071C3.90237 12.3166 3.90237 11.6834 4.29289 11.2929C4.68342 10.9024 5.31658 10.9024 5.70711 11.2929L9.29289 14.8787C9.68342 15.2692 10.3166 15.2692 10.7071 14.8787L19.2929 6.29289C19.6834 5.90237 20.3166 5.90237 20.7071 6.29289Z"
                          fill="#008000"/>
                  </svg>
                </div>
              </ng-container>
              <ng-container *ngSwitchCase="'dateAgo'">
                {{element.createdAt |dateAgo}}
              </ng-container>
              <ng-container *ngSwitchCase="'status'">
                <p class="font-semibold"
                   [ngClass]="element[column.columnDef] === 'NEW' ? 'text-yellow-500' : 'text-green-600'">
                  {{element[column.columnDef]}}
                </p>
              </ng-container>
              <ng-container *ngSwitchCase="'reason'">
                <p class="max-h-16 overflow-y-auto whitespace-pre">
                  {{element.text}}
                </p>
              </ng-container>
              <ng-container *ngSwitchCase="'rating'">
                <svg *ngIf="element.rating == 'thumb up'" width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="0.5" width="32" height="32" rx="4" fill="#0D1023"/>
                  <path d="M13.7812 25H21.8438C22.5797 25 23.1844 24.55 23.4469 23.9031L26.1234 17.5563C26.2031 17.35 26.2453 17.1344 26.2453 16.9V15.0906C26.2453 14.1016 25.4484 13 24.4734 13H18.8859L19.7297 9.17969L19.7578 8.89844C19.7578 8.52813 19.6078 8.19062 19.3688 7.94687L18.4219 7L12.5156 12.9672C12.1969 13.2906 12 13.7406 12 14.2375V23.2375C12 24.2266 12.8063 25 13.7812 25ZM6.75 14.5H9.75V25H6.75V14.5Z" fill="#E1E4E8"/>
                </svg>
                <svg *ngIf="element.rating == 'thumb down'" width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="0.5" width="32" height="32" rx="4" fill="#0D1023"/>
                  <path d="M19.2188 7H11.1562C10.4203 7 9.81563 7.45 9.55313 8.09688L6.87187 14.4438C6.79219 14.65 6.75 14.8656 6.75 15.1V16.9094C6.75 17.8984 7.54688 19 8.52188 19H14.1141L13.2703 22.8203L13.2422 23.1016C13.2422 23.4719 13.3922 23.8094 13.6313 24.0531L14.5688 24.9953L20.4844 19.0328C20.8031 18.7094 21 18.2594 21 17.7625V8.7625C21 7.77344 20.1938 7 19.2188 7ZM23.25 7H26.25V17.5H23.25V7Z" fill="#E1E4E8"/>
                </svg>

              </ng-container>
              <ng-container *ngSwitchDefault>
                {{element[column.columnDef]}}
              </ng-container>
            </ng-container>
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>


    <mat-paginator [pageIndex]='pageIndex'
                   [pageSize]='limit'
                   [length]='count'
                   (page)='changePage($event)'
                   [pageSizeOptions]="[5, 10]"
                   aria-label="Select page">
    </mat-paginator>
  </div>
</div>

<ng-template #confirm>
  <div class="p-6">
    <div class="header border-b flex justify-between items-center pb-3">
      <div class="text-xl font-bold card-title text-white">
        Resolved feedback
      </div>
      <div class="rounded-full flex hover:bg-slate-200 cursor-pointer">
        <svg (click)="dialogService.closeAll()" width="24" height="24" viewBox="0 0 24 24" fill="none"
             xmlns="http://www.w3.org/2000/svg">
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"/>
        </svg>
      </div>
    </div>
    <!--    content-->
    <div class="content mt-6  text-white">
      <div>Are you sure ?</div>
    </div>
    <!--    footer-->
    <div class="footer mt-6 flex justify-end">
      <button mat-raised-button class="mr-3" (click)="dialogService.closeAll()"
      >
        <span>cancel</span>
      </button>
      <button mat-raised-button color="primary" class="mr-3" (click)="resolvedFeedback();dialogService.closeAll()">
        <span>yes</span>
      </button>
    </div>
  </div>
</ng-template>
