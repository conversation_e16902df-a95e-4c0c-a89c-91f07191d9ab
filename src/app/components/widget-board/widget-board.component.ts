import {highlight} from 'sql-highlight';
import {Component, Input, OnInit, Injector, ViewChild, AfterViewInit, ViewEncapsulation} from '@angular/core';
import {format} from 'sql-formatter';
import {BaseComponent} from "@core/base.component";
import {AddOrEditChartComponent} from "../../chart-component/add-or-edit-chart.component";
import {MatTableDataSource} from '@angular/material/table';
import {cloneDeep, random} from "lodash-es";
import {MatPaginator} from '@angular/material/paginator';
import {CommonUtilsService} from "@shared/common-utils.service";
import {v4 as uuidv4} from "uuid";
import defaultColor from "../../shared/models/general.model";

@Component({
    selector: 'app-widget-board',
    templateUrl: './widget-board.component.html',
    styleUrls: ['./widget-board.component.scss'],
  encapsulation: ViewEncapsulation.None // <-- Disable view encapsulation

})
export class WidgetBoardComponent extends BaseComponent implements OnInit, AfterViewInit {
    @ViewChild(MatPaginator) paginator: MatPaginator;

    @Input() data: any = [];
    @Input() config: any;
    @Input() sql: any;
    @Input() answer : any;
    commonUtilsService = CommonUtilsService;

    colors = defaultColor

    displayedColumns: string[] = [];
    dataSource: MatTableDataSource<any>;
    columnDef: any = [];
    isSQLHovered = false;
    btnCopyText = 'Copy';

    constructor(
        injector: Injector,
    ) {
        super(injector);
    }

    ngOnInit() {
        if (!this.config) {
            this.config = {
                "nameChart": "hello",
                "chartType": "column",
            }
        };
      this.sql = format(this.sql.trim(), {language: 'sql'});
      this.sql = highlight(this.sql, {html: true});
        if (this.data) {
            this.data.forEach((item: any) => {
                if (typeof item.revenue === 'string' && /^\d+$/.test(item.revenue)) {
                    item.revenue = parseInt(item.revenue, 10);
                }
            });
            this.config = this.createNewConfig(this.config)
            this.displayedColumns = this.getAllKeys(this.data);
            this.dataSource = new MatTableDataSource<any>(this.data);
            this.columnDef = this.createColumnDef(this.displayedColumns);
        };
    }

    ngAfterViewInit() {
        if (this.dataSource) {
            this.dataSource.paginator = this.paginator;
        };
    }

    activeTab = 0; // Initialize with the index of the default active tab

    onTabChanged(event: number) {
        this.activeTab = event; // Update the active tab index
    }

    getAllKeys(data): string[] {
        const keySet = data.reduce((keys, item) => {
            return new Set([...keys, ...Object.keys(item)]);
        }, new Set<string>());

        return Array.from(keySet) as string[];
    }

    createColumnDef(column: any[]) {
        return column.map(item => {
            const obj = {header: ''};
            obj.header = item;
            return obj;
        });
    }


    changeColor(data: any) {
      this.config.series.forEach(serie => {
        if (serie.uniqueValues)
          serie.uniqueValues.forEach(unique => {
            if (unique.idSeries == data.idSeries) {
              unique.color = data.color;
            }
          })
      })
      this.config = cloneDeep(this.config);
    }

    changeNameChart($event: string) {
        this.config.nameChart = $event;
        this.config = cloneDeep(this.config);
    }

    createNewConfig(config: any) {
        let newConfig = {
            nameChart: config.nameChart,
            xAxis: "",
            series: [],
        }

        const convertedData = this.data.map(item => {
            for (const key in item) {
                if (!isNaN(Number(item[key]))) {
                    item[key] = Number(item[key]);
                }
            }
            return item;
        });
        const keysArray = Object.keys(convertedData[0]).map(key => ({
            name: key,
            type: typeof convertedData[0][key],
            value: key
        }));

        const stringFields = keysArray.filter(item => item.type === 'string');
        const numberFields = keysArray.filter(item => item.type === 'number')

        numberFields.forEach(numberField => {
            newConfig.series.push({
                chartType: config.chartType,
                name: "",
                value: "",
                formatType: "currency",
                suffix: null,
                inputText: "GROUP",
                sizePie: 200,
                innerSize: 60,
                uniqueValues: [],
                dataLabel: true,
            })
        })

        if (!newConfig.xAxis && stringFields.length > 0) {
            newConfig.xAxis = stringFields[0].name;
        }
        newConfig.series.forEach((series, index) => {
            this.updateSeries(series, numberFields, stringFields, index)
        })

        newConfig.series.forEach(series => {
            let uniqueValues = []
            switch (series.inputText) {
                case "GROUP":
                    uniqueValues = [...new Set(this.data.map(item => item[series.name]))];
                    break;
                case "SUM":
                    uniqueValues = [undefined];
                default:
                    break;
            }
            const array1 = uniqueValues;
            const array2 = series.uniqueValues
            if (!array2 || !array1.some(value => array2.map(item => item.name).includes(value))) {
                series.uniqueValues = uniqueValues.map((value) => ({
                    name: value,
                    idSeries: uuidv4(),
                    color: this.chooseRandomColor(),
                    dataLabel: true,
                }));
            }
        });
        return newConfig
    }

    chooseRandomColor(){
      const colorLeft = this.colors.length;
      const randomIndex = random(colorLeft - 1);
      const colorPicked = this.colors[randomIndex];
      this.colors.splice(randomIndex, 1);
      if (this.colors.length === 0){
        this.colors = defaultColor;
      }
      return colorPicked
    }

    updateSeries(serie, numberFields, stringFields, index) {
        if (!serie.value && numberFields.length > 0) {
            serie.value = numberFields[index].name;
        }
        if (!serie.name && stringFields.length > 1) {
            serie.name = stringFields[1].name;
        } else {
            serie.name = numberFields[index].name;
            serie.inputText = 'SUM'
        }
        // if (!serie.formatType) {
        //   serie.formatType = this.listFormat[0].code
        // }
    }

    onCopyClick(divContent: HTMLPreElement) {
        console.log('Copy clicked');
        navigator.clipboard.writeText(divContent.innerText);
        this.btnCopyText = 'Copied!'
        setTimeout(() => {
            this.btnCopyText = 'Copy';
        }, 1000);
    }
}
