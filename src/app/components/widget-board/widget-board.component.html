<div class=" p-4 border-2 rounded h-full w-full">
    <mat-tab-group (selectedIndexChange)="onTabChanged($event)" mat-stretch-tabs="false" mat-align-tabs="start">
        <mat-tab>
            <ng-template mat-tab-label>
                <svg [style.fill]="activeTab === 0 ? '#5200FF' : '#76726F'" width="18" height="18" viewBox="0 0 18 18"
                     xmlns="http://www.w3.org/2000/svg">
                    <path
                            d="M0 18V0H18V18H0ZM2 6H16V2H2V6ZM7.325 11H10.675V8H7.325V11ZM7.325 16H10.675V13H7.325V16ZM2 11H5.325V8H2V11ZM12.675 11H16V8H12.675V11ZM2 16H5.325V13H2V16ZM12.675 16H16V13H12.675V16Z"/>
                </svg>
            </ng-template>
            <div class="m-4 min-w-[350px]">
                <table *ngIf="dataSource" mat-table [dataSource]="dataSource" class="no-shadow">
                    <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
                        <th mat-header-cell *matHeaderCellDef class="header-table"> {{ column }}</th>
                        <td mat-cell *matCellDef="let element">{{ element[column] | customNumberPipe}}
                        </td>
                    </ng-container>
                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
                <div *ngIf="!dataSource">No data </div>
                <mat-paginator class="w-full" *ngIf="dataSource" [pageSizeOptions]="[5, 10, 20, 30]"
                               showFirstLastButtons
                               aria-label="Select page of periodic elements">
                </mat-paginator>
            </div>
        </mat-tab>
        <mat-tab  *ngIf="dataSource && answer!= 'No data'">
            <ng-template mat-tab-label>
                <svg [style.fill]="activeTab === 1 ? '#5200FF' : '#76726F'" width="20" height="18" viewBox="0 0 20 18"
                     fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 8.78L18.24 1.45L19.97 2.45L14.74 11.5L8.23 7.75L3.46 16H20V18H0V0H2V14.54L7.5 5L14 8.78Z"
                    />
                </svg>
            </ng-template>
            <app-chart class="w-full" *ngIf="data"  [config]="config" [data]="data" [isEditable]="true"
                       (seriesColorChange)="changeColor($event)"
                       (nameChange)="changeNameChart($event)"
            ></app-chart>
        </mat-tab>
        <mat-tab>
            <ng-template mat-tab-label>
                <svg [style.stroke]="activeTab === 2 ? '#5200FF' : '#76726F'" width="24" height="24" viewBox="0 0 24 24"
                     fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                            d="M17 8V16H21M13 15L14 16M3 15C3 15.2652 3.10536 15.5196 3.29289 15.7071C3.48043 15.8946 3.73478 16 4 16H6C6.26522 16 6.51957 15.8946 6.70711 15.7071C6.89464 15.5196 7 15.2652 7 15V13C7 12.7348 6.89464 12.4804 6.70711 12.2929C6.51957 12.1054 6.26522 12 6 12H4C3.73478 12 3.48043 11.8946 3.29289 11.7071C3.10536 11.5196 3 11.2652 3 11V9C3 8.73478 3.10536 8.48043 3.29289 8.29289C3.48043 8.10536 3.73478 8 4 8H6C6.26522 8 6.51957 8.10536 6.70711 8.29289C6.89464 8.48043 7 8.73478 7 9M12 8C12.5304 8 13.0391 8.21071 13.4142 8.58579C13.7893 8.96086 14 9.46957 14 10V14C14 14.5304 13.7893 15.0391 13.4142 15.4142C13.0391 15.7893 12.5304 16 12 16C11.4696 16 10.9609 15.7893 10.5858 15.4142C10.2107 15.0391 10 14.5304 10 14V10C10 9.46957 10.2107 8.96086 10.5858 8.58579C10.9609 8.21071 11.4696 8 12 8Z"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </ng-template>
            <div (mouseover)="isSQLHovered = true" (mouseleave)="isSQLHovered = false">
                <div *ngIf="isSQLHovered" class="copy-icon" (click)="onCopyClick(divContent)">
                    <mat-icon>content_copy</mat-icon> {{this.btnCopyText}}
                </div>
                <pre #divContent [innerHTML]="sql"></pre>
            </div>
        </mat-tab>
    </mat-tab-group>
    <!--  <div *ngIf="currentTab === 'TABLE'">-->
    <!--    <table mat-table [dataSource]="dataSource">-->

    <!--      &lt;!&ndash; Position Column &ndash;&gt;-->
    <!--      <div *ngFor="let key of columnDef">-->
    <!--        <ng-container [matColumnDef]="key.name">-->
    <!--          <th mat-header-cell *matHeaderCellDef> No. </th>-->
    <!--          <td mat-cell *matCellDef="let element"> {{ element[key.name] }} </td>-->
    <!--        </ng-container>-->
    <!--      </div>-->


    <!--      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>-->
    <!--      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>-->
    <!--    </table>-->

    <!--    <mat-paginator [pageSizeOptions]="[5, 10, 20]"-->
    <!--                   showFirstLastButtons-->
    <!--                   aria-label="Select page of periodic elements">-->
    <!--    </mat-paginator>-->
    <!--  </div>-->


</div>
