<ng-container>
  <div class="flex flex-col h-full relative bg-zinc-100">
    <!--        header-->
    <div class="header border-b flex justify-between items-center relative bg-white p-4">
      <div class="text-2xl font-bold card-title capitalize">
        {{'Edit' }} chart
      </div>
      <div class="flex">
        <button mat-raised-button class="mr-3" (click)="dialogRef.close()">
          <span>{{'Cancel'}}</span>
        </button>
        <button mat-raised-button color="primary"
                (click)="save()">
          <span>{{'Save'}}</span>
        </button>
      </div>
    </div>
    <!--        content-->
    <div class="content flex h-full p-4 gap-4">
      <div class="flex flex-col h-full w-2/3">
        <div class="bg-white rounded-3xl pt-8">
          <div class="flex gap-8 px-8 pb-4">
            <div (click)="chooseTypeChart('pie')">
              <svg [ngClass]="{ 'active-class': config.series[0].chartType === 'pie' }" width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.333008" width="48" height="48" rx="4" fill="#F5F7FA"/>
                <path d="M23.8237 25.5093C23.2714 25.5093 22.8237 25.0616 22.8237 24.5093V12.1184C22.8237 11.5368 22.3276 11.0746 21.7529 11.164C15.2934 12.1683 10.333 17.7568 10.333 24.4685C10.333 31.9628 16.3702 38 23.8646 38C30.5762 38 36.1647 33.0397 37.1691 26.5801C37.2584 26.0054 36.7962 25.5093 36.2146 25.5093H23.8237Z" fill="#706EE7"/>
                <path d="M25.961 10.101C25.3911 10.0247 24.9058 10.4846 24.9058 11.0597V22.4275C24.9058 22.9798 25.3535 23.4275 25.9058 23.4275H37.2736C37.8486 23.4275 38.3085 22.9421 38.2322 22.3722C37.3835 16.0301 32.3031 10.9497 25.961 10.101Z" fill="#29C287"/>
              </svg>
              <p class="text-center">Pie</p>
            </div>
            <div (click)="chooseTypeChart('line')">
              <svg [ngClass]="{ 'active-class': config.series[0].chartType === 'line' }" width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="1.11084" y="1" width="46" height="46" rx="3" fill="#F5F7FA"/>
                <path d="M24.1919 34.303C23.8398 34.522 23.3891 34.501 23.059 34.2503L17.3992 29.9526C17.025 29.6685 16.5035 29.6831 16.1459 29.9878L13.3129 32.4015C12.6145 32.9965 11.559 32.8795 11.0079 32.1459C10.5035 31.4745 10.6074 30.5268 11.2453 29.9806L15.9493 25.953C16.3046 25.6489 16.8231 25.6315 17.1979 25.9113L23.2596 30.4359C23.5906 30.6829 24.0393 30.7011 24.3892 30.4817L35.1055 23.7599C35.8681 23.2815 36.8752 23.5291 37.3291 24.3064C37.7643 25.0517 37.5274 26.008 36.7945 26.4639L24.1919 34.303Z" fill="#29B983"/>
                <path d="M24.1919 24.7508C23.8398 24.9698 23.3891 24.9488 23.059 24.6981L17.3992 20.4003C17.025 20.1162 16.5035 20.1309 16.1459 20.4356L13.3129 22.8492C12.6145 23.4443 11.559 23.3272 11.0079 22.5937C10.5035 21.9223 10.6074 20.9745 11.2453 20.4284L15.9493 16.4008C16.3046 16.0966 16.8231 16.0793 17.1979 16.359L23.2596 20.8837C23.5906 21.1307 24.0393 21.1489 24.3892 20.9294L35.1055 14.2076C35.8681 13.7293 36.8752 13.9768 37.3291 14.7542C37.7643 15.4995 37.5274 16.4558 36.7945 16.9117L24.1919 24.7508Z" fill="#706EE7"/>
              </svg>
              <p class="text-center">Line</p>
            </div>
            <div (click)="chooseTypeChart('bar')">
              <svg [ngClass]="{ 'active-class': config.series[0].chartType === 'bar' }" width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.666504" width="48" height="48" rx="4" fill="#F5F7FA"/>
                <rect x="36.6665" y="10.6667" width="5.33333" height="26.6667" rx="1.5" transform="rotate(90 36.6665 10.6667)" fill="#29C287"/>
                <rect x="23.3335" y="18" width="5.33333" height="13.3333" rx="1.5" transform="rotate(90 23.3335 18)" fill="#706EE7"/>
                <rect x="19.3335" y="32.6667" width="5.33333" height="9.33333" rx="1.5" transform="rotate(90 19.3335 32.6667)" fill="#706EE7"/>
                <rect x="28.6665" y="25.3334" width="5.33333" height="18.6667" rx="1.5" transform="rotate(90 28.6665 25.3334)" fill="#29C287"/>
              </svg>
              <p class="text-center">Bar</p>
            </div>
            <div (click)="chooseTypeChart('column')">
              <svg [ngClass]="{ 'active-class': config.series[0].chartType === 'column' }" width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg" transform="rotate(-90)">
                <rect x="0.666504" width="48" height="48" rx="4" fill="#F5F7FA"/>
                <rect x="36.6665" y="10.6667" width="5.33333" height="26.6667" rx="1.5" transform="rotate(90 36.6665 10.6667)" fill="#29C287"/>
                <rect x="23.3335" y="18" width="5.33333" height="13.3333" rx="1.5" transform="rotate(90 23.3335 18)" fill="#706EE7"/>
                <rect x="19.3335" y="32.6667" width="5.33333" height="9.33333" rx="1.5" transform="rotate(90 19.3335 32.6667)" fill="#706EE7"/>
                <rect x="28.6665" y="25.3334" width="5.33333" height="18.6667" rx="1.5" transform="rotate(90 28.6665 25.3334)" fill="#29C287"/>
              </svg>
              <p class="text-center">Column</p>
            </div>
          </div>
          <div class=" px-8 py-4 border-t ">
            <app-chart *ngIf="data" [config]="configChart" [data]="data" [isEditable]="false"
                       (seriesColorChange)="changeColor($event)"
                       (nameChange)="changeNameChart($event)"
            ></app-chart>
          </div>
        </div>

        <!--<div class="h-full flex flex-col">
          <div class="tab px-10 py-2 flex">
            <div
              class="tab-item"
              *ngFor="let tab of tabs"
              [ngClass]="{ active: currentTab === tab.type }"
              (click)="changeTabs(tab.type)"
            >
              <span>{{ tab.name  }}</span>
            </div>
          </div>
          <div class=" mx-4 border-2	rounded h-full">
            <div *ngIf="currentTab == 'QUERY'">
              <button mat-raised-button color="primary" (click)="getData()">
                <span>Áp dụng</span>
              </button>
              <div class=" mb-4">
                <mat-radio-group [(ngModel)]="config.typeData"
                                 class="flex flex-row justify-around w-full"
                                 (change)="updateConfig()">
                  <mat-radio-button value="RAW">RAW DATA</mat-radio-button>
                  <mat-radio-button value="SQL">SQL</mat-radio-button>
                </mat-radio-group>
              </div>
              <div class="border-2 rounded w-[90%]">
                 <textarea class="w-full"
                           [placeholder]="'Sql ...'"
                           [(ngModel)]="sqlStr"
                           [ngModelOptions]="{standalone: true}"
                           matInput trim='blur'
                           rows="10"></textarea>
              </div>
            </div>
            <div *ngIf="currentTab == 'TRANSFORM'">
              day la transform
            </div>
          </div>
        </div>-->
      </div>
      <div class=" flex h-full flex-col w-1/3 ">
        <div class="h-full flex	">
          <div class=" border-2	rounded-3xl h-full flex w-full  overflow-auto  bg-white">
            <mat-tab-group class="w-full" (selectedIndexChange)="onTabChanged($event)">
              <mat-tab >
                <ng-template mat-tab-label>
                  <h1 [style.color]="activeTab === 0 ? '#5200FF' : '#76726F'" class="font-medium	text-lg	">Data</h1>
                </ng-template>
                <div class="w-full p-8">
                  <div class="flex justify-between w-full mb-4">
                    <h1>Series</h1>
                    <button mat-raised-button color="primary"
                            (click)="createNewSeries()">{{'New' }}
                    </button>
                  </div>
                  <mat-accordion multi>
                    <div cdkDropList (cdkDropListDropped)="drop($event)">
                      <div class="w-full example-box mb-4" *ngFor="let serie of config.series;let i = index" cdkDrag>
                        <mat-expansion-panel>
                          <mat-expansion-panel-header>
                            <mat-panel-title>
                              Serie {{i + 1}}
                            </mat-panel-title>
                            <button
                              [disabled]="config.series.length < 2"
                              class="flex items-center mx-4"
                              (click)="deleteOptional(i)"
                            >
                              <svg [attr.stroke]="config.series.length < 2 ? '#808080' : '#ED2E2E'" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 5.98047C17.67 5.65047 14.32 5.48047 10.98 5.48047C9 5.48047 7.02 5.58047 5.04 5.78047L3 5.98047"  stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8.5 4.97L8.72 3.66C8.88 2.71 9 2 10.69 2H13.31C15 2 15.13 2.75 15.28 3.67L15.5 4.97"  stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M18.8504 9.14062L18.2004 19.2106C18.0904 20.7806 18.0004 22.0006 15.2104 22.0006H8.79039C6.00039 22.0006 5.91039 20.7806 5.80039 19.2106L5.15039 9.14062" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M10.3301 16.5H13.6601"  stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M9.5 12.5H14.5"  stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                              </svg>

                            </button>
                          </mat-expansion-panel-header>
                          <div class="flex gap-2 flex-col">
                            <mat-label >Chart type</mat-label>
                            <app-search-input class=" w-full"
                                              [setOptionValue]="'name'"
                                              [setOptionLabel]="'name'"
                                              [value]="serie.chartType"
                                              (valueChange)="serie.chartType = $event ;  updateConfig(); updateSeries(serie)"
                                              [listOptions]="listChartType"
                                              [isRequire]="true"
                                              [setCanBeSearch]="false"
                                              [haveTooltip]="false"
                            >
                            </app-search-input>
                          </div>
                          <div class="flex gap-2 flex-col">
                            <mat-label *ngIf="i == 0">xAxis</mat-label>
                            <app-search-input-extended *ngIf="i == 0" class=" w-full"
                                                       [setOptionValue]="'value'"
                                                       [setOptionLabel]="'name'"
                                                       [value]="config.xAxis"
                                                       (valueChange)="config.xAxis = $event ;  updateConfig()"
                                                       [listOptions]="keysArray"
                                                       [isRequire]="true"
                                                       [setCanBeSearch]="false"
                                                       [haveTooltip]="false"
                            >
                            </app-search-input-extended>
                          </div>

                          <div class="flex gap-2 flex-col">
                            <mat-label>Value</mat-label>
                            <app-search-input-extended
                                                       [setOptionValue]="'value'"
                                                       [setOptionLabel]="'name'"
                                                       [value]="serie.value"
                                                       (valueChange)="serie.value = $event ; updateConfig() ;"
                                                       [listOptions]="keysArray"
                                                       [isRequire]="true"
                                                       [setCanBeSearch]="false"
                                                       [haveTooltip]="false"
                            >
                            </app-search-input-extended>
                          </div>

                          <div class="w-full flex-col flex">
                            <mat-label [ngClass]="'required'"
                                       class="mb-1 ml-[1px]">{{'legend' }}</mat-label>
                            <div class=" mb-4">
                              <mat-radio-group [(ngModel)]="serie.inputText"
                                               class="flex flex-row justify-around w-full"
                                               (change)="updateConfig()">
                                <mat-radio-button value="GROUP">Group by</mat-radio-button>
                                <mat-radio-button value="SUM">Sum</mat-radio-button>
                              </mat-radio-group>
                            </div>
                            <div class="w-full">
                              <app-search-input-extended class=" w-full"
                                                         *ngIf="serie.inputText == 'GROUP'"
                                                         [setOptionValue]="'value'"
                                                         [setOptionLabel]="'name'"
                                                         [value]="serie.name"
                                                         (valueChange)="serie.name = $event; updateConfig() ;"
                                                         [listOptions]="keysArray"
                                                         [setCanBeSearch]="false"
                                                         [haveTooltip]="false"
                              >
                              </app-search-input-extended>
                              <div class="flex flex-col w-full">
                                <input class="border rounded-md px-4 h-10 border-slate-300
                                        {{ serie.inputText !== 'SUM' ? 'bg-gray-50 text-gray-600 cursor-not-allowed' : '' }}"
                                       type="text"
                                       *ngIf="serie.inputText == 'SUM'"
                                       [(ngModel)]="serie.name"
                                       (ngModelChange)="updateConfig()"
                                       [ngModelOptions]="{standalone: true}">
                              </div>
                            </div>
                          </div>
                          <div>
                            <mat-checkbox [(ngModel)]="serie.isDrilldown" [ngModelOptions]="{standalone: true}"
                                          class="mt-[20px]">
                              {{'Drilldown'}}
                            </mat-checkbox>
                            <mat-label  *ngIf="serie.isDrilldown">name</mat-label>
                            <app-search-input-extended class=" w-full"
                                                       *ngIf="serie.isDrilldown"
                                                       [setOptionValue]="'value'"
                                                       [setOptionLabel]="'name'"
                                                       [value]="serie.drilldownName"
                                                       (valueChange)="serie.drilldownName = $event;updateDrilldown(serie)"
                                                       [listOptions]="keysArray"
                                                       [isRequire]="true"
                                                       [setCanBeSearch]="false"
                                                       [haveTooltip]="false"
                            >
                            </app-search-input-extended>
                          </div>
                        </mat-expansion-panel>
                      </div>
                    </div>
                  </mat-accordion>
                </div>
              </mat-tab>
              <mat-tab>
                <ng-template mat-tab-label>
                  <h1 [style.color]="activeTab === 1 ? '#5200FF' : '#76726F'" class="font-medium	text-lg">Configuartion</h1>
                </ng-template>
                <div class="w-full p-8">
                  <h1 class="font-medium	text-gray-500	">Gerneral</h1>
<!--                  <div class="w-full flex flex-col">-->
<!--                    <mat-label-->
<!--                      class="mb-1 ml-[1px]">Tên bảng-->
<!--                    </mat-label>-->
<!--                    <input class="border rounded-md px-4 h-10 border-slate-300	" type="text"-->
<!--                           [(ngModel)]="config.nameChart" (change)="updateConfig()"-->
<!--                           [ngModelOptions]="{standalone: true}">-->
<!--                  </div>-->
                  <div class="w-full flex flex-col mb-2">
                    <mat-label
                      class="mb-1 ml-[1px] text-sm	font-semibold	">Y axis Name
                    </mat-label>
                    <input class="border rounded-md px-4 h-10 border-slate-300	" type="text"
                           [(ngModel)]="config.nameYAxis" (change)="updateConfig()"
                           [ngModelOptions]="{standalone: true}">
                  </div>
                  <div class="flex flex-col w-full mb-2">
                    <mat-label
                      class="mb-1 ml-[1px] mr-4 text-sm	font-semibold	">Max Y axis value
                    </mat-label>
                    <input class="border rounded-md px-4 h-10 border-slate-300	" type="number"
                           [(ngModel)]="config.YAxisMax" (ngModelChange)="updateConfig()"
                           [ngModelOptions]="{standalone: true}">
                  </div>
                  <div class="flex items-center gap-4">
                    <mat-checkbox matInput [(ngModel)]="config.shared" (change)="updateConfig()"> Shared</mat-checkbox>
                  </div>
                  <mat-label>Type</mat-label>
                  <app-search-input
                    [setOptionValue]="'name'"
                    [setOptionLabel]="'label' "
                    [value]="config.category"
                    (valueChange)="config.category  = $event;"
                    [listOptions]="ListCategories"
                    [isRequire]="true"
                    [setCanBeSearch]="false"
                    [haveTooltip]="false"
                  >
                  </app-search-input>
                  <mat-accordion multi>
                    <h1 class="font-medium	text-gray-500 mb-4">Series</h1>
                    <div *ngFor="let serie of config.series; let i = index" class="mb-4">
                      <mat-expansion-panel class="mb-4">
                        <mat-expansion-panel-header>
                          <mat-panel-title>
                            <h1 class=""> serie {{i + 1}}</h1>
                          </mat-panel-title>
                        </mat-expansion-panel-header>
                        <div class="flex gap-2 w-full">
                          <div class="w-1/2">
                            <mat-label>Format</mat-label>
                            <app-search-input class="w-full"
                                              [setOptionValue]="'code'"
                                              [setOptionLabel]="'name'"
                                              [value]="serie.formatType"
                                              (valueChange)="serie.formatType = $event ; updateConfig() ;"
                                              [listOptions]="listFormat"
                                              [isRequire]="true"
                                              [setCanBeSearch]="false"
                                              [haveTooltip]="false"
                            >
                            </app-search-input>
                          </div>
                          <div class=" w-1/2">
                            <mat-label>{{"Suffix" }}</mat-label>
                            <input class="border rounded-md px-4 h-10 border-slate-300	" [(ngModel)]="serie.suffix" (change)="updateConfig()" [ngModelOptions]="{standalone: true}" type="text">
                          </div>
                        </div>
                        <div *ngIf="serie.chartType == 'pie'">
                          <div>
                            <div class="flex w-full flex-col">
                              <div>
                                <mat-label
                                  class="mb-1 ml-[1px] mr-4">Vị trí
                                </mat-label>
                                <div class="flex gap-2">
                                  <input class="border rounded-md px-4 h-10 border-slate-300	w-1/2" type="number"
                                         [(ngModel)]="serie.piePositionX" (ngModelChange)="updateConfig()"
                                         [ngModelOptions]="{standalone: true}" placeholder="vị trí x">
                                  <input class="border rounded-md px-4 h-10 border-slate-300	w-1/2" type="number"
                                         [(ngModel)]="serie.piePositionY" (ngModelChange)="updateConfig()"
                                         [ngModelOptions]="{standalone: true}" placeholder="vị trí y">
                                  <div class="flex items-center">
                                    <mat-checkbox matInput [(ngModel)]="serie.pieIsSum" (change)="updateConfig()"> Tổng
                                    </mat-checkbox>
                                  </div>
                                </div>
                              </div>
                              <div class="flex">
                                <mat-label
                                  class="mb-1 ml-[1px] mr-4">Pie size
                                </mat-label>
                                <input class="border rounded-md px-4 h-10 border-slate-300	" type="number"
                                       [(ngModel)]="serie.sizePie" (ngModelChange)="updateConfig()"
                                       [ngModelOptions]="{standalone: true}">
                              </div>
                              <div class="flex">
                                <mat-label
                                  class="mb-1 ml-[1px] mr-4">Inner size ratio
                                </mat-label>
                                <input class="border rounded-md px-4 h-10 border-slate-300	" type="number"
                                       [(ngModel)]="serie.innerSize" (ngModelChange)="updateConfig()"
                                       [ngModelOptions]="{standalone: true}">
                              </div>
                            </div>
                          </div>
                        </div>
                        <mat-label>Sort by</mat-label>
                        <app-search-input
                          class="mb-4"
                          *ngIf="serie.inputText ==='SUM' && config.series.length == 1 && keysArray.length <=2"
                          [setOptionValue]="'name'"
                          [setOptionLabel]="'label'"
                          [value]="serie.sortBy"
                          (valueChange)="serie.sortBy  = $event; updateConfig()"
                          [listOptions]="listSort"
                          [setCanBeSearch]="false"
                          [haveTooltip]="false"
                        >
                        </app-search-input>
                        <div class="flex items-center gap-4">
                          <mat-checkbox matInput [(ngModel)]="serie.dataLabel" (ngModelChange)="updateDataLabel(i)">
                            Show data number
                          </mat-checkbox>
                          <app-search-input
                            *ngIf="serie.dataLabel && serie.inputText !=='SUM'"
                            [setOptionValue]="'name'"
                            [setOptionLabel]="'name'"
                            [value]="serie.listLabel"
                            (valueChange)="updateDataLabel(i, $event) ;"
                            [listOptions]="serie.uniqueValues"
                            [setCanBeSearch]="false"
                            [multiple]="true"
                          >
                          </app-search-input>
                        </div>


                        <div class="flex items-center">
                          <mat-checkbox matInput [(ngModel)]="serie.isStack" (change)="updateStack(i)">Stack
                          </mat-checkbox>
                        </div>
                      </mat-expansion-panel>
                    </div>
                  </mat-accordion>
                </div>
              </mat-tab>
            </mat-tab-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>



