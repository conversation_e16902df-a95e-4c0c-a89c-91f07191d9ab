import moment from 'moment';
import {DATE_FORMAT, DATE_TIME_FORMAT} from '@shared/app.constant';
import {Subject} from "rxjs";

export class CommonUtilsService {
  static noLoadTable$: Subject<any> = new Subject<any>();

  public static swapElements(arr, index1, index2) {
    // <PERSON><PERSON>m tra xem chỉ số có hợp lệ không
    if (index1 < 0 || index1 >= arr.length || index2 < 0 || index2 >= arr.length) {
      return;
    }

    // Đổi chỗ giá trị của hai phần tử
    let temp = arr[index1];
    arr[index1] = arr[index2];
    arr[index2] = temp;
  }

  public static formatCurrency(value: any): any {
    if (value == null || value.toString().trim() === ''
      || isNaN(value = value.toString().replace(/,/g, ''))) {
      return null;
    }
    // value = value.toString().replace(/,/g, '');
    if (parseFloat(value).toString().indexOf('.') > -1) {
      const values = parseFloat(value).toString().split('.');
      // eslint-disable-next-line radix
      return [String(parseInt(values[0], 0)).replace(/(.)(?=(\d{3})+$)/g, '$1,'), values[1]].join('.');
    } else {
      if (value.startsWith('-')) {
        // eslint-disable-next-line radix
        return `-${String(Math.abs(parseInt(value, 0))).replace(/(.)(?=(\d{3})+$)/g, '$1,')}`;
      } else {
        // eslint-disable-next-line radix
        return String(parseInt(value, 0)).replace(/(.)(?=(\d{3})+$)/g, '$1,');
      }

    }
  }

  public static dateToString(value: any, isFullDate?: boolean, utc?: boolean): any {
    if (value == null || value.toString().trim() === '') {
      return null;
    }
    let a;
    if (isNaN(value)) {
      if (value.length === DATE_FORMAT.length || value.length === DATE_TIME_FORMAT.length) {
        return value;
      }
      a = moment(value);
    } else {
      a = moment(Number(value));
    }

    if (!a.isValid()) {
      return value;
    }
    if (isFullDate) {
      if (utc) {
        return moment(a).utc().format(DATE_TIME_FORMAT);
      }
      return a.format(DATE_TIME_FORMAT);
    }
    return a.format(DATE_FORMAT);
  }

  public static stringToDate(value: any, isFullDate?: boolean) {
    if (value == null || value.toString().trim() === '') {
      return null;
    }
    if (isFullDate) {
      return moment(value, DATE_TIME_FORMAT).toDate();
    }
    return moment(value, DATE_FORMAT).toDate();
  }

  public static formatVND(value: any, isFullDate?: boolean) {
    const VND = new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });

    if (value || parseInt(value) === 0) {
      value = VND.format(parseInt(value));
    }
    return value;
  }

  public static filterValueByRegex(nationalId, regex: RegExp): string {
    return nationalId.replace(regex, ' ')
  }

  // public static filterEmail(email, regex: RegExp): string {
  //   return email.replace(regex, ' ')
  // }


  public static formatRemoveVietnameseTones(keySearch): string {
    keySearch = keySearch.replace(/\s(?=\s)/g, '').trim();
    return this.removeVietnameseTones(keySearch);
  }

  // format theo yyyy/mm/dd
  public static returnDateFormat(iso: Date) {
    if (iso) {
      const date = new Date(iso);
      if (date.getDate() < 10) {
        if (date.getMonth() + 1 < 10) {
          return `${date.getFullYear()}/0${date.getMonth() + 1}/0${date.getDate()}`;
        } else {
          return `${date.getFullYear()}/${date.getMonth() + 1}/0${date.getDate()}`;
        }
      } else {
        if (date.getMonth() + 1 < 10) {
          return `${date.getFullYear()}/0${date.getMonth() + 1}/${date.getDate()}`;
        }
      }
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
    } else {
      return null
    }
  }

  // format theo dd/mm/yyy

  public static toFormattedDate(iso: any) {
    const date = new Date(iso);
    if (date.getDate() < 10) {
      if (date.getMonth() + 1 < 10) {
        return `0${date.getDate()}/0${date.getMonth() + 1}/${date.getFullYear()}`;
      } else {
        return `0${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
      }
    } else {
      if (date.getMonth() + 1 < 10) {
        return `${date.getDate()}/0${date.getMonth() + 1}/${date.getFullYear()}`;
      }
    }
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
  }

  public static formatSearchListRemoveVietnameseTones(keySearch: string, list = []): any {
    return list.filter((x) => {
      const name = x.name ? x.name : x.nameShow;
      const checkSearch = CommonUtilsService.formatRemoveVietnameseTones(name).includes(CommonUtilsService.formatRemoveVietnameseTones(keySearch));
      if (checkSearch) {
        x.showSearch = 1;
      } else {
        x.showSearch = 2;
      }
      return x;
    });
  }

  private static removeVietnameseTones(str: string): string {
    str = str.toLowerCase();
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    // Some system encode vietnamese combining accent as individual utf-8 characters
    // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, "");
    str = str.trim();
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    // str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,"");
    return str;
  }

  public static converRGBtoArr(rgb){
    const rgbaString = rgb;

    const rgbaValues = rgbaString.substring(rgbaString.indexOf("(") + 1, rgbaString.lastIndexOf(")"));

    const rgbaArray = rgbaValues.split(",");

    const red = parseInt(rgbaArray[0].trim());
    const green = parseInt(rgbaArray[1].trim());
    const blue = parseInt(rgbaArray[2].trim());
    const alpha = parseFloat(rgbaArray[3].trim());

    return [red, green, blue, alpha];
  }

  public static convertArrtoRGB(arr){
    const rgbaArray = arr;
    const red = rgbaArray[0];
    const green = rgbaArray[1];
    const blue = rgbaArray[2];
    const alpha = rgbaArray[3];

    return  `rgba(${red}, ${green}, ${blue}, ${alpha})`;

  }

  public  static formatWithUnit(value: number, type : string): string {
    if (isNaN(value)) {
      return 'Invalid';
    }
    const rawValue = value

    const suffixes = ["", "K", "M", "B"];
    let suffixIndex = 0;

    while (value >= 1000 && suffixIndex < suffixes.length - 1) {
      value /= 1000;
      suffixIndex++;
    }

    let formattedValue = value.toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 3});
    // if(type == "currency"){
    //   return formattedValue + ' ' + suffixes[suffixIndex]  + 'VND';
    // } else  {
    //   return formattedValue + ' ' + suffixes[suffixIndex];
    // }
    switch (type){
      case 'currency':
        return formattedValue + ' ' + suffixes[suffixIndex]  + ' VND';
      case 'number':
        return formattedValue + ' ' + suffixes[suffixIndex];
      default:
        return rawValue.toString();
    }
  }

  public static extractTextDoubleQuotes(str){
    const re = /"(.*?)"/g;
    const result = [];
    let current;
    while (current = re.exec(str)) {
      result.push(current.pop());
    }
    return result.length > 0 ? result : [str];
  }

  // public static base64url(source: any) {
  //   // Encode in classical base64
  //   let encodedSource = CryptoJS.enc.Base64.stringify(source);
  //   // Remove padding equal characters
  //   encodedSource = encodedSource.replace(/=+$/, '');
  //   // Replace characters according to base64url specifications
  //   encodedSource = encodedSource.replace(/\+/g, '-');
  //   encodedSource = encodedSource.replace(/\//g, '_');

  //   return encodedSource;
  // }

  // public static encodeToken(data:any, secretKey: string) {
  //   const header = { alg: 'HS256', typ: 'JWT' };
  //   const stringifiedHeader = CryptoJS.enc.Utf8.parse(JSON.stringify(header));
  //   const encodedHeader = this.base64url(stringifiedHeader);
  //   const stringifiedData = CryptoJS.enc.Utf8.parse(JSON.stringify(data));
  //   const encodedData = this.base64url(stringifiedData);
  //   const signature = CryptoJS.HmacSHA256(`${encodedHeader}.${encodedData}`, secretKey);
  //   return `${encodedHeader}.${encodedData}.${this.base64url(signature)}`;
  // }

}
