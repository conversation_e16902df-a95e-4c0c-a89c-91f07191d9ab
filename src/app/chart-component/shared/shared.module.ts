import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartComponent} from "./chart/chart.component";
import { ConfirmDialogComponent} from "./confirm-dialog/confirm-dialog.component";
import {MatCardModule} from "@angular/material/card";
import {MatIconModule} from "@angular/material/icon";
import {MatButtonModule} from "@angular/material/button";
import {MatInputModule} from "@angular/material/input";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatSelectModule} from "@angular/material/select";
import {MatTableModule} from "@angular/material/table";
import {MatRadioModule} from "@angular/material/radio";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatCheckboxModule} from "@angular/material/checkbox";
import {MatDialogModule} from "@angular/material/dialog";
import {MatAutocompleteModule} from "@angular/material/autocomplete";
import {MatListModule} from "@angular/material/list";
import {MatTooltipModule} from "@angular/material/tooltip";
import {MatMomentDateModule} from '@angular/material-moment-adapter';
import {ColorPickerComponent} from "./color-picker/color-picker.component";
import {SearchInputComponent} from "./search-input/search-input.component";
import {SearchInputExtendedComponent} from "./search-input-extended/search-input-extended.component";
import {ColorPaletteComponent} from "@shared/color-picker/color-palette/color-palette.component";
import {NgxMatSelectSearchModule} from "ngx-mat-select-search";
import {ColorSlideComponent} from "@shared/color-picker/color-slide/color-slide.component";
import {FormsModule} from "@angular/forms";
import {HttpClientModule} from "@angular/common/http";
import {CdkAccordionModule} from '@angular/cdk/accordion';
import {MatExpansionModule} from "@angular/material/expansion";
import {MatPaginatorModule} from "@angular/material/paginator";

export const MAT_MODULES = [
  MatCardModule,
  MatIconModule,
  MatButtonModule,
  MatInputModule,
  MatFormFieldModule,
  MatSelectModule,
  MatTableModule,
  MatRadioModule,
  MatDatepickerModule,
  MatMomentDateModule,
  MatCheckboxModule,
  MatDialogModule,
  MatAutocompleteModule,
  // MatSelectFilterModule,
  MatListModule,
  MatTooltipModule,
  MatPaginatorModule
];

@NgModule({
  declarations: [
    ChartComponent,
    ConfirmDialogComponent,
    ColorPickerComponent,
    SearchInputComponent,
    SearchInputExtendedComponent,
    ColorPaletteComponent,
    ColorSlideComponent,

  ],
  imports: [
    CommonModule,
    ...MAT_MODULES,

    NgxMatSelectSearchModule,
    FormsModule,
    HttpClientModule,
    CdkAccordionModule

  ],
  exports: [
    ChartComponent,
    ConfirmDialogComponent,
    ...MAT_MODULES,
    ColorPickerComponent,
    SearchInputComponent,
    SearchInputExtendedComponent,
    FormsModule,
    MatExpansionModule,
  ]
})
export class SharedModule { }
