import {ChangeDetectorRef, Component, Injector, OnInit, SimpleChanges} from '@angular/core';
import {SearchInputComponent} from "@shared/search-input/search-input.component";

@Component({
  selector: 'app-search-input-extended',
  templateUrl: './search-input-extended.component.html',
  styleUrls: ['./search-input-extended.component.scss']
})
export class SearchInputExtendedComponent extends SearchInputComponent implements OnInit {

  constructor(
    cdr: ChangeDetectorRef
  ) {
    super(cdr);
  }

  ngOnInit(): void {

  }

  ngOnChanges(changes: SimpleChanges) {
    super.ngOnChanges(changes);
    if (this.value) {
      const fieldExists = this.listOptions.some(item => item.value === this.value);
      if (!fieldExists) {
        this.listOptions.push({name: this.value + ' (undefined)', value: this.value});
      }
    }

  }

}
