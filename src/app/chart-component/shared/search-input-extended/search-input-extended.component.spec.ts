import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SearchInputExtendedComponent } from './search-input-extended.component';

describe('SearchInputExtendedComponent', () => {
  let component: SearchInputExtendedComponent;
  let fixture: ComponentFixture<SearchInputExtendedComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SearchInputExtendedComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SearchInputExtendedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
