<!--    header-->
<div class="header border-b flex justify-between items-center pb-3">
    <div class="text-xl font-bold card-title">
        {{(data?.title ||  'specialText.delete_2')}}
    </div>
    <div>
        <button mat-icon-button class="hover:bg-slate-200" (click)="closeOneCdkBackdropReverse()">
            <mat-icon  title="{{'close'}}" class="icon-size-5"></mat-icon>
        </button>
    </div>
</div>
<!--    content-->
<div class="content mt-6">
    <div>{{ (data?.content || 'specialText.delete_1')  }}</div>
    <div *ngIf="data?.subContent">{{ data?.subContent  }}</div>
</div>
<!--    footer-->
<div class="footer mt-6 flex justify-end">
    <button mat-raised-button class="mr-3" (click)="closeOneCdkBackdropReverse()"
    >
        <span>{{'button.cancel'}}</span>
    </button>
    <button mat-raised-button color="primary" class="mr-3"  (click)="dialogRef.close( 'success'); dialogRef.close(isConfirm)">
        <span>{{'button.yes'}}</span>
    </button>
</div>
