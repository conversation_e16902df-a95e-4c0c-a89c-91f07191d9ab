import {
  AfterViewInit,
  Component,
  ElementRef, EventEmitter, Injector,
  Input,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
  OnChanges,
  Renderer2
} from '@angular/core';
import * as Highcharts from 'highcharts';
import HighchartsMore from 'highcharts/highcharts-more';
import HighchartsExporting from 'highcharts/modules/exporting';
import HighchartsData from 'highcharts/modules/data';
import HighchartsDrilldown from 'highcharts/modules/drilldown';
import HighchartsAccessibility from 'highcharts/modules/accessibility';
import HighchartsHeatmap from 'highcharts/modules/heatmap';
import {BaseWidget} from "@shared/chart/base-widget.component";
// import {ChartService} from "@shared/chart.service";
import {ColorPickerComponent} from "@shared/color-picker/color-picker.component";
import {CommonUtilsService} from "@shared/common-utils.service";
import {CustomChartConfig} from "@shared/app.constant";
import {cloneDeep} from "lodash-es";
import {AddOrEditChartComponent} from "../../add-or-edit-chart.component";

HighchartsMore(Highcharts);
HighchartsExporting(Highcharts);
HighchartsData(Highcharts);
HighchartsDrilldown(Highcharts);
HighchartsAccessibility(Highcharts);
HighchartsHeatmap(Highcharts);

@Component({
  selector: 'app-chart',
  templateUrl: './chart.component.html',
  styleUrls: ['./chart.component.scss']
})
export class ChartComponent extends BaseWidget implements OnInit, OnChanges {
  @ViewChild('editName') editName?: ElementRef;
  highcharts = Highcharts;
  // chart: Highcharts.Chart;
  chartOptions: Highcharts.Options;

  @Input() data: any;

  @Input() config: any;

  @Output() seriesColorChange: EventEmitter<any> = new EventEmitter<any>();

  @Output() nameChange = new EventEmitter<string>();

  @Input() isEditable: boolean = true

  constructor(
    injector: Injector,
    private el: ElementRef,
    private renderer: Renderer2
  ) {
    super(injector);
  }

  ngOnInit(): void {
    console.log(this.chartOptions)
    this.mountResize();
    this.chartOptions = this.generateChartOptions(this.config, this.data);
    // this.chart = Highcharts.chart('chart-container', this.chartOptions);
  }

  ngAfterViewInit(): void {
    this.updateChartOptions();
    this.createEditName()
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.config) {
      this.chartOptions = this.generateChartOptions(this.config, this.data);
    }
    this.updateChartOptions();
    this.createEditName()
  }

  createEditName() {
    const titleElement = this.el.nativeElement.querySelector('.highcharts-title');
    const svgContent = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13.2603 3.59924L5.05034 12.2892C4.74034 12.6192 4.44034 13.2692 4.38034 13.7192L4.01034 16.9592C3.88034 18.1292 4.72034 18.9292 5.88034 18.7292L9.10034 18.1792C9.55034 18.0992 10.1803 17.7692 10.4903 17.4292L18.7003 8.73924C20.1203 7.23924 20.7603 5.52924 18.5503 3.43924C16.3503 1.36924 14.6803 2.09924 13.2603 3.59924Z" stroke="#4C5153" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M11.8896 5.05078C12.3196 7.81078 14.5596 9.92078 17.3396 10.2008" stroke="#4C5153" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M3 22H21" stroke="#4C5153" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>`;

    if (titleElement) {
      // Tạo một thẻ div mới
      const svgContainer = this.renderer.createElement('div');
      svgContainer.innerHTML = svgContent;

      // Chèn nội dung vào thẻ div mới
      const buttonElement = this.renderer.createElement('button');
      this.renderer.appendChild(buttonElement, svgContainer);

      svgContainer.addEventListener('click', () => {
        this.showDialog(this.editName)

      });

      // Chèn thẻ div mới vào cạnh thẻ có class 'highcharts-title'
      this.renderer.appendChild(titleElement, buttonElement);
    }
  }

  generateChartOptions(config, data) {
    const categories: string[] = [];
    let sortType: string = "DEFAULT";
    if (config.series.length === 1) {
      sortType = config.series[0].sortBy
    }
    switch (sortType) {
      case "INCREASE":
        data.sort(function (a, b) {
          return a[config.series[0].value] - b[config.series[0].value];
        });
        break;
      case "DECREASE":
        data.sort(function (a, b) {
          return b[config.series[0].value] - a[config.series[0].value];
        });
        break;
      default:
        break;
    }
    data.forEach(item => {
      const category = item[config.xAxis];
      if (category && !categories.includes(category)) {
        categories.push(category);
      }
    });
    const seriesData = [];

    config.series.forEach((series: CustomChartConfig) => {
      if (series.uniqueValues)
        if (series.chartType != 'pie') {
          series.uniqueValues.forEach((value: any) => {
            const serie = {
              type: series.chartType,
              name: series.inputText == "GROUP" ? value.name ?? series.name : series.name,
              id: value.idSeries,
              data: [],
              color: value.color,
              tooltip: {},
              stack: value.stack,
              stacking: value.stack ? "normal" : null,
              dataLabels: {
                enabled: series.dataLabel ? value.dataLabel : false,
                useHTML: true
              },
            };
            switch (series.inputText) {
              case "GROUP":
                categories.forEach(category => {
                  const filteredData = data.filter(item => item[config.xAxis] === category && item[series.name] === value.name);
                  let total = filteredData.reduce((sum, item) => sum + parseInt(item[series.value], 10), 0);
                  serie.data.push(total);
                });
                break;
              case "SUM":
                categories.forEach(category => {
                  const filteredData = data.filter(item => item[config.xAxis] === category);
                  let total = filteredData.reduce((sum, item) => sum + parseInt(item[series.value], 10), 0);
                  serie.data.push(total);
                });
                break;
              default:
                break;
            }

            serie.tooltip = {
              customTooltipPerSeries: createCustomTooltipFormatter,
            }
            seriesData.push(serie);
          });
        } else {
          let serie = {
            type: series.chartType,
            data: [],
            tooltip: {},
            // center: [series.piePositionX, series.piePositionY],
            size: series.sizePie,
            innerSize: series.innerSize + '%',
          };
          series.uniqueValues.forEach((value: any, index) => {
            let seriePieChart: any = {
              name: value.name ?? series.name,
              id: value.idSeries,
              color: value.color,
              y: 0,
              drilldown: value.idSeries,
            }
            if (series.pieIsSum && index == 0) {
              seriePieChart = {
                ...seriePieChart,
                dataLabels: {
                  enabled: true,
                  distance: -series.sizePie / 2,
                  format: '{point.total} M',
                  style: {
                    fontSize: '15px',
                    color: 'black'
                  }
                }
              }
            }
            const filteredData = data.filter(item => item[series.name] === value.name);
            seriePieChart.y = filteredData.reduce((sum, item) => sum + parseInt(item[series.value], 10), 0);
            serie.data.push(seriePieChart)
          })
          serie.tooltip = {
            customTooltipPerSeries: createCustomTooltipFormatter,
          }
          seriesData.push(serie);
        }

      function createCustomTooltipFormatter(item) {
        if (!chartOptions.tooltip.shared) {
          return this.series.name + ':' + CommonUtilsService.formatWithUnit(this.y, series.formatType) + '' + (series.suffix ?? '');
        } else {
          return item.series.name + ':' + CommonUtilsService.formatWithUnit(item.y, series.formatType) + '' + (series.suffix ?? '') + '<br>';
        }
      }
    })

    let chartOptions: Highcharts.Options = {
      title: {
        text: this.config.nameChart,
        align: "left",
        useHTML: true
      },
      xAxis: {
        categories,
      },
      legend: {
        enabled: true
      },
      credits: {
        enabled: false
      },
      yAxis: {
        title: {
          text: this.config.nameYAxis,
        },
        labels: {
          formatter: function () {
            const label = this.axis.defaultLabelFormatter.call(this);
            if (Number(this.value) / 1000000000 >= 1 && Number(this.value) / 1000000000 < 1000 || Number(this.value) / 1000000000 <= -1 && Number(this.value) / 1000000000 > -1000) {
              return Number(this.value) / 1000000000 + 'B';
            }
            return label;
          }
        },
        max: this.config.YAxisMax
      },
      plotOptions: {
        column: {
          stacking: 'normal',
        },
        line: {
          stacking: 'normal',
        },
      },
      tooltip: {
        useHTML: true,
        shared: this.config.shared,
        formatter: function () {
          let tooltipContent = '';
          tooltipContent += this.key.toString() + '<br>'

          // @ts-ignore
          if (chartOptions.tooltip.shared) {
            this.points.forEach(item => {
              var seriesName = item.series.name;
              var pointValue = item.y;
              // @ts-ignore
              if (item.series?.tooltipOptions?.customTooltipPerSeries) {
                // @ts-ignore
                tooltipContent += item.series.tooltipOptions.customTooltipPerSeries.call(this, item);
              } else {
                tooltipContent += seriesName + ':' + pointValue + '<br>';
              }
            });
          } else {
            // @ts-ignore
            if (this.series?.tooltipOptions?.customTooltipPerSeries) {
              // @ts-ignore
              tooltipContent += this.series?.tooltipOptions?.customTooltipPerSeries.call(this);
            } else {
              tooltipContent += this.series.name + ':' + this.y;
            }
          }
          return tooltipContent;
        }
      },
      series: seriesData,
    };

    // Gán sự kiện click vào thẻ SVG

    if (config.series.find(serie => serie.isDrilldown)) {
      chartOptions = this.updateDrilldown(chartOptions)
    }
    chartOptions = this.addColorPickerHandle(chartOptions)

    if (this.isEditable) {
      chartOptions = this.updateActionExporting(chartOptions, 1)
    }
    // chartOptions  = this.updateAction(chartOptions)
    return chartOptions;
  }

  //handle drilldown
  updateDrilldown(chartOptions: Highcharts.Options): Highcharts.Options {
    const options = cloneDeep(chartOptions);
    const drilldownList: any = {
      series: []
    };

    const categories: string[] = [];
    this.data.forEach(item => {
      const category = item[this.config.xAxis];
      if (category && !categories.includes(category)) {
        categories.push(category);
      }
    });

    this.config.series.forEach(serie => {
      serie.uniqueValues.forEach(unique => {
        const drilldownEle = {
          type: serie.chartType,
          id: unique.idSeries,
          data: [],
          center: [serie.piePositionX, serie.piePositionY],
          size: serie.sizePie,
          innerSize: serie.innerSize + '%',
        };
        unique.drilldown.forEach(drilldown => {
          const filteredData = this.data.filter(item => item[serie.drilldownName] === drilldown.name);
          let total = filteredData.reduce((sum, item) => sum + parseInt(item[serie.value], 10), 0);
          const dataEle = [drilldown.name, total]
          drilldownEle.data.push(dataEle)
        })
        drilldownList.series.push(drilldownEle)
      })
    })

    options.drilldown = drilldownList
    return options
  }


  //handle color picker
  addColorPickerHandle(chartOptions: Highcharts.Options): Highcharts.Options {
    const options : Highcharts.Options = cloneDeep(chartOptions);
    const globalThis = this;

    function itemClick(event) {
      const mousePos = event.browserEvent;
      const rect = this.legendItem.label.element.getBoundingClientRect();
      const isClickingOnLegend = rect.x <= mousePos.clientX && mousePos.clientX <= (rect.x + rect.width);
      if (isClickingOnLegend) {
        return true;
      } else {
        globalThis.showDialog(ColorPickerComponent,
          {
            data: {color: this.options.color},
            width: '400px',
          }
          , (color) => {
            if (color) {
              let dataChangeColor = {
                idSeries: this.id ?? this.userOptions.id,
                color: color
              };
              globalThis.seriesColorChange.emit(dataChangeColor)
              globalThis.updateChartOptions()
            }
          });
        ;
        return false;
      }
    }

    options.plotOptions = {
      ...options.plotOptions,
      series: {
        events: {
          legendItemClick: itemClick
        }
      },
      pie: {
        innerSize: '80%',
        showInLegend: true,
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          style: {
            textDecoration: 'none'
          },
          formatter: function () {
            return '<b>' + this.point.name + '</b>: ' + this.point.percentage.toFixed(1) + ' %';
          },
          useHTML: true
        },
        point: {
          events: {
            legendItemClick: itemClick
          }
        }
      }
    }

    return options;
  }

  //update action in series
  updateAction(chartOptions: Highcharts.Options): Highcharts.Options {
    const options = cloneDeep(chartOptions);
    const globalThis = this
    this.config.series.forEach(serie => {
      serie.uniqueValues.forEach(unique => {
        if (unique.drilldownId) {
          // console.log(unique.drilldownId)
          // console.log(options.series.find(item => item.id === unique.idSeries))
          const seriesItem = options.series.find(item => item.id === unique.idSeries);
          if (seriesItem) {
            seriesItem.events = {
              click: function (event) {
                // globalThis.chartService.getOneChart(unique.drilldownId).subscribe(res => {
                //   const JSONConfig = JSON.parse(res.data.config);
                //   globalThis.data = JSONConfig.dataChart.data;
                //   globalThis.config = JSONConfig.config;
                //   globalThis.chartOptions = globalThis.generateChartOptions(globalThis.data, globalThis.config);
                //   globalThis.updateChartOptions();
                // });
              },
            };
          } else {
            console.error("Không tìm thấy đối tượng có idSeries khớp.");
          }
        }
      })
    });
    return options;
  }

  updateChartOptions() {
    if (this.highChartElm) {
      const highChartElement = this.highChartElm.nativeElement;
      this.chart = Highcharts.chart(highChartElement, this.chartOptions);
    }
  }

  ngOnDestroy(): void {
    this.unMountResize();
  }

  private updateActionExporting(chartOptions: Highcharts.Options, id?: number) {
    const options = cloneDeep(chartOptions);
    const globalThis = this

    options.exporting = {
      menuItemDefinitions: {
        // Custom definition
        editTable: {
          onclick: function () {
            const ref = globalThis.showDialog(AddOrEditChartComponent,
              {
                data: {
                  data: globalThis.data,
                  config: globalThis.config
                },
                width: '100vw',
                maxWidth: '98vw',
                height: '100vh',
                maxHeight: '100vh'
                // disableClose: true
              }, (value) => {
              }
            );
          },
          text: 'Edit widget'
        }
      },
      buttons: {
        contextButton: {
          menuItems: ['editTable', "viewFullscreen", "printChart", "separator", "downloadPNG", "downloadJPEG", "downloadPDF", "downloadSVG"]
        }
      }
    }
    return options;
  }

  changeName() {
    this.nameChange.emit(this.config.nameChart);
  }
}
