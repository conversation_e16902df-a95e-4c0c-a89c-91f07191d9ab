<div #highChartRef id="chart-container" style=" height: 400px;" class="min-w-[500px] lg:w-full"></div>
<ng-template #editName>
  <div class="p-4">
    <div class="pb-3">
      <p class="text-[16px] font-medium">Edit Table Name</p>
    </div>
    <div >
      <input class="border rounded-md px-4 h-10 border-slate-300	w-full" type="text"
             [(ngModel)] ="config.nameChart " (ngModelChange)="changeName()"
             [ngModelOptions]="{standalone: true}">
    </div>
  </div>
</ng-template>
