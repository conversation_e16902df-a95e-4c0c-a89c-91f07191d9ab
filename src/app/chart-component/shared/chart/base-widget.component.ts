import {Subject} from "rxjs";
import {Directive, ElementRef, Injector, Input, Type, ViewChild} from "@angular/core";
import * as Highcharts from "highcharts";
import {BaseComponent} from "@core/base.component";

@Directive()
export class BaseWidget extends BaseComponent {
  @Input() resizeSub: Subject<any>;
  @ViewChild('highChartRef') highChartElm?: ElementRef;
  chart: Highcharts.Chart;

  constructor(
    injector: Injector,
  ) {
    super(injector);
  }


  mountResize(): void {
    if (this.resizeSub) {
      this.resizeSub.subscribe(() => {
        this.resizeChart();
      })
    }
  }

  resizeChart() {
    if (this.highChartElm && this.chart) {
      const containerWidth = this.highChartElm.nativeElement.offsetWidth;
      const containerHeight = this.highChartElm.nativeElement.offsetHeight;

      this.chart.setSize(containerWidth, containerHeight, true);
    }
  }

  unMountResize(): void {
    if (this.resizeSub) {
      this.resizeSub.unsubscribe();
    }
  }

}
