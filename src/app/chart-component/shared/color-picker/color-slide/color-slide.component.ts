import {
  Component,
  ViewChild,
  ElementRef,
  AfterViewInit,
  Output,
  HostListener,
  EventEmitter, Input,
  SimpleChanges,
  OnChanges,
} from '@angular/core'
import {CommonUtilsService} from "@shared/common-utils.service";

@Component({
  selector: 'app-color-slide',
  templateUrl: './color-slide.component.html',
  styleUrls: ['./color-slide.component.scss']
})
export class ColorSlideComponent implements AfterViewInit, OnChanges {

  @ViewChild('canvas')
  canvas: ElementRef<HTMLCanvasElement>

  @Input()
  colorHue: string

  XValue: number

  @Output()
  color: EventEmitter<string> = new EventEmitter()

  // gradientStops = [
  //   [0, [255, 0, 0, 1]],
  //   [0.04, [255, 63, 0, 1]],
  //   [0.08, [255, 127, 0, 1]],
  //   [0.13, [255, 191, 0, 1]],
  //   [0.17, [255, 255, 0, 1]],
  //   [0.21, [191, 255, 0, 1]],
  //   [0.25, [127, 255, 0, 1]],
  //   [0.29, [63, 255, 0, 1]],
  //   [0.33, [0, 255, 0, 1]],
  //   [0.38, [0, 255, 63, 1]],
  //   [0.42, [0, 255, 127, 1]],
  //   [0.46, [0, 255, 191, 1]],
  //   [0.5, [0, 255, 255, 1]],
  //   [0.54, [0, 191, 255, 1]],
  //   [0.58, [0, 127, 255, 1]],
  //   [0.63, [0, 63, 255, 1]],
  //   [0.67, [0, 0, 255, 1]],
  //   [0.71, [63, 0, 255, 1]],
  //   [0.75, [127, 0, 255, 1]],
  //   [0.79, [191, 0, 255, 1]],
  //   [0.83, [255, 0, 255, 1]],
  //   [0.88, [255, 0, 191, 1]],
  //   [0.92, [255, 0, 127, 1]],
  //   [0.96, [255, 0, 63, 1]],
  //   [1, [255, 0, 0, 1]]
  // ];

  gradientStops = [
    [0, [255, 0, 0, 1]],
    [0.17, [255, 255, 0, 1]],
    [0.34, [0, 255, 0, 1]],
    [0.51, [0, 255, 255, 1]],
    [0.68, [0, 0, 255, 1]],
    [0.85, [255, 0, 255, 1]],
    [1, [255, 0, 0, 1]]
  ];

  private ctx: CanvasRenderingContext2D
  private mousedown: boolean = false
  private selectedHeight: number

  canvasWidth = 352

  constructor() {
  }

  ngAfterViewInit() {
    this.draw();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['colorHue']) {
      const colorArr = CommonUtilsService.converRGBtoArr(changes['colorHue'].currentValue);
      const colorArrDraft = colorArr
      let minIndex = colorArrDraft.indexOf(Math.min(...colorArr));
      let maxIndex = colorArrDraft.indexOf(Math.max(...colorArr));
      const indicesToRemove = [minIndex, maxIndex, 3];
      this.XValue = +colorArrDraft.filter((value, index) => !indicesToRemove.includes(index));
      const nearestStop = this.findNearestColorStop(colorArr, this.gradientStops);
      const heightBase = this.gradientStops[nearestStop][0]
      const nextStop = nearestStop + 1
      const biasBase = (+this.gradientStops[nextStop][0] - +this.gradientStops[nearestStop][0]) / 255
      if (nearestStop == 0 || nearestStop == 2 || nearestStop == 4) {
        this.selectedHeight = (+heightBase + +this.XValue * biasBase) * this.canvasWidth;
      } else {
        this.selectedHeight = (+heightBase + (255 - +this.XValue) * biasBase) * this.canvasWidth;
      }
      this.draw();
    }
  }

  draw() {
    if (this.canvas) {
      if (!this.ctx) {
        this.ctx = this.canvas.nativeElement.getContext('2d');
      }
      const width = this.canvas.nativeElement.width;
      const height = this.canvas.nativeElement.height;

      this.ctx.clearRect(0, 0, width, height)

      const gradient = this.ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, 'rgba(255, 0, 0, 1)');
      gradient.addColorStop(0.17, 'rgba(255, 255, 0, 1)');
      gradient.addColorStop(0.34, 'rgba(0, 255, 0, 1)');
      gradient.addColorStop(0.51, 'rgba(0, 255, 255, 1)');
      gradient.addColorStop(0.68, 'rgba(0, 0, 255, 1)');
      gradient.addColorStop(0.85, 'rgba(255, 0, 255, 1)');
      gradient.addColorStop(1, 'rgba(255, 0, 0, 1)');

      this.ctx.beginPath();
      this.ctx.rect(0, 0, width, height);

      this.ctx.fillStyle = gradient;
      this.ctx.fill();
      this.ctx.closePath();

      if (this.selectedHeight) {
        this.ctx.beginPath();
        this.ctx.strokeStyle = 'white';
        this.ctx.lineWidth = 3;
        this.ctx.rect(this.selectedHeight - 5, 0, 10, 20);
        this.ctx.stroke();
        this.ctx.closePath();
      }
    }
  }

  @HostListener('window:mouseup', ['$event'])
  onMouseUp(evt: MouseEvent) {
    this.mousedown = false;
  }

  onMouseDown(evt: MouseEvent) {
    this.mousedown = true;
    this.selectedHeight = evt.offsetX;
    const setY = 10
    let setX = 0
    if (evt.offsetX < 0) {
      setX = 0
    } else if (evt.offsetX > 351) {
      setX = 351
    } else {
      setX = evt.offsetX
    }
    this.draw();
    this.emitColor(setX, setY);
  }

  onMouseMove(evt: MouseEvent) {
    if (this.mousedown) {
      this.selectedHeight = evt.offsetX;
      const setY = 10
      let setX = 0
      if (evt.offsetX < 0) {
        setX = 0
      } else if (evt.offsetX > 351) {
        setX = 351
      } else {
        setX = evt.offsetX
      }
      this.draw();
      this.emitColor(setX, setY);
    }
  }

  emitColor(x: number, y: number) {
    const rgbaColor = this.getColorAtPosition(x, y);
    console.log(rgbaColor)
    this.color.emit(rgbaColor);
  }

  getColorAtPosition(x: number, y: number) {
    const imageData = this.ctx.getImageData(x, y, 1, 1).data;
    return (
      'rgba(' + imageData[0] + ',' + imageData[1] + ',' + imageData[2] + ',1)'
    );
  }

  findNearestColorStop(color, gradientStops) {
    let nearestIndex = -1;
    let distanceArr = []

    for (let i = 0; i < gradientStops.length; i++) {
      const stop = gradientStops[i];
      const stopColor = stop[1];
      const distance = this.colorDistance(color, stopColor);
      distanceArr.push(distance)

    }
    let minIndex = distanceArr.indexOf(Math.min(...distanceArr));
    if (minIndex == 0) {
      if (distanceArr[1] < distanceArr[5]) {
        nearestIndex = 0
      } else {
        nearestIndex = 5
      }
    } else {
      if (distanceArr[minIndex - 1] < distanceArr[minIndex + 1]) {
        nearestIndex = minIndex - 1
      } else {
        nearestIndex = minIndex
      }
    }
    return nearestIndex;
  }

  colorDistance(color1, color2) {
    // Calculate the Euclidean distance between two colors
    const rDiff = color1[0] - color2[0];
    const gDiff = color1[1] - color2[1];
    const bDiff = color1[2] - color2[2];

    return Math.sqrt(rDiff ** 2 + gDiff ** 2 + bDiff ** 2);
  }


}
