import {Component, Output, EventEmitter, Injector, Inject} from '@angular/core'
import {BaseComponent} from "@core/base.component";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {CommonUtilsService} from "@shared/common-utils.service";


@Component({
  selector: 'app-color-picker',
  templateUrl: './color-picker.component.html',
  styleUrls: ['./color-picker.component.scss']
})
export class ColorPickerComponent extends BaseComponent {
  public hue: string
  public color: string
  hexColor: string
  redColor: number = 0
  greenColor: number = 0
  blueColor: number = 0
  hueChangeSlide: number = 0

  colors = [
    "rgba(255, 92, 118, 1)",    // Hồng phấn (đậm hơn)
    "rgba(33, 150, 243, 1)",    // Xanh pastel (đậm hơn)
    "rgba(255, 255, 0, 1)",     // <PERSON><PERSON><PERSON> chanh
    "rgba(255, 0, 80, 1)",      // Đỏ anh đào (đậm hơn)
    "rgba(118, 242, 118, 1)",   // Xanh mint (đậm hơn)
    "rgba(200, 200, 250, 1)",   // Tím lavendar (đậm hơn)
    "rgba(255, 95, 0, 1)",      // <PERSON> đào (đậm hơn)
    "rgba(70, 65, 60, 1)",      // X<PERSON>m tro (đậm hơn)
    "rgba(0, 105, 148, 1)",     // Xanh d<PERSON>ơng bi<PERSON>n
    "rgba(92, 64, 51, 1)",      // Nâu cappuccino
    "rgba(255, 253, 208, 1)",   // Vàng kem
    "rgba(0, 128, 128, 1)",     // Xanh ngọc
    "rgba(255, 0, 0, 1)",       // Đỏ tươi
    "rgba(255, 20, 147, 1)",    // Hồng đào
    "rgba(255, 223, 0, 1)",     // Vàng nắng
    "rgba(0, 128, 0, 1)",       // Xanh lá cây
    "rgba(49, 46, 129, 1)",     // Tím than
    "rgba(0, 0, 0, 1)",         // Đen đen
    "rgba(255, 69, 0, 1)",      // Cam nóng
    "rgba(0, 71, 171, 1)",      // Xanh dương cobalt
    "rgba(255, 105, 180, 1)",   // Hồng phớt
    "rgba(139, 69, 19, 1)",     // Nâu socola
    "rgba(218, 165, 32, 1)",    // Vàng hạt dẻ
    "rgba(0, 255, 255, 1)",     // Xanh aqua
    "rgba(139, 0, 0, 1)",       // Đỏ thẫm
    "rgba(148, 0, 211, 1)",     // Tím đậm
    "rgba(154, 185, 115, 1)",   // Xanh olivine
    "rgba(218, 112, 214, 1)"    // Hồng tím
  ];


  constructor(
    injector: Injector,
    public dialogRef: MatDialogRef<ColorPickerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    super(injector);
    this.color = data.color
    this.hue = this.caculatorHue(this.color)
    this.hexColor = this.convertRgbaToHex(this.color)
    this.extractRgbaValues(this.color)
  }

  makeColor() {
    this.dialogRef.close(this.color)
  }

  chooseColor(color, type: string) {
    // console.log(color, this.color, type)
    this.color = color
    if (type !== 'hex') {
      this.hexColor = this.convertRgbaToHex(color)
    }
    if (type !== 'rgb') {
      this.extractRgbaValues(color)
    }
    if (type !== 'palette') {
      this.hue = this.caculatorHue(color)
    }
  }


  caculatorHue(color) {
    //thuật toán tìm Hue:
    //B1 tìm số lớn nhất chuyển thành 255 và số bé nhất chuyển thành 0
    //B2 số trung bình sẽ được tính theo công thức dưới để sao Hue sẽ có dạng 1 số 255 và 1 sô 0 số còn lại là x ví dụ (x , 255, 0) hoặc (255, 0, x) ...
    const colorArr = CommonUtilsService.converRGBtoArr(color);
    colorArr.pop();
    let minIndex = colorArr.indexOf(Math.min(...colorArr));
    let maxIndex = colorArr.indexOf(Math.max(...colorArr));
    let middleIndex = 3 - minIndex - maxIndex;
    let minNumber = colorArr[minIndex];
    let maxNumber = colorArr[maxIndex];
    let middleNumber = colorArr[middleIndex];
    let findXinHue
    if (maxNumber - minNumber == 0) {
      findXinHue = 3
    } else {
      findXinHue = Math.ceil((middleNumber - minNumber) * 255 / (maxNumber - minNumber));
    }
    const newHue = [0, 0, 0];
    newHue[maxIndex] = 255;
    newHue[middleIndex] = findXinHue;
    newHue.push(1);
    return CommonUtilsService.convertArrtoRGB(newHue)
  }

  convertHexToRgb() {
    if (this.hexColor.length == 6) {
      const r = parseInt(this.hexColor.slice(0, 2), 16);
      const g = parseInt(this.hexColor.slice(2, 4), 16);
      const b = parseInt(this.hexColor.slice(4, 6), 16);
      const colorFromHex = `rgba(${r}, ${g}, ${b}, 1)`;
      this.chooseColor(colorFromHex, 'hex')
    }
  }

  convertRgbToHex(r: number, g: number, b: number): string {
    const hexR = r.toString(16).padStart(2, '0');
    const hexG = g.toString(16).padStart(2, '0');
    const hexB = b.toString(16).padStart(2, '0');
    return `${hexR}${hexG}${hexB}`;
  }

  convertRgbaToHex(rgba: string): string {
    const rgbaValues = rgba.match(/\d+/g);

    if (rgbaValues && rgbaValues.length === 4) {
      const r = parseInt(rgbaValues[0]);
      const g = parseInt(rgbaValues[1]);
      const b = parseInt(rgbaValues[2]);
      return this.convertRgbToHex(r, g, b);
    }

    return '';
  }

  inputRGB(event, color) {
    const value = isNaN(parseInt(event.target.value)) ? 0 : parseInt(event.target.value);
    const clampedValue = value > 255 ? Math.floor(value / 10) : value;

    switch (color) {
      case 'red':
        this.redColor = event.type === 'blur' ? clampedValue : value;
        break;
      case 'green':
        this.greenColor = event.type === 'blur' ? clampedValue : value;
        break;
      case 'blue':
        this.blueColor = event.type === 'blur' ? clampedValue : value;
        break;
      default:
        break;
    }

    const isValid = this.redColor <= 255 && this.greenColor <= 255 && this.blueColor <= 255;

    if (isValid) {
      const colorFromRGB = `rgba(${this.redColor}, ${this.greenColor}, ${this.blueColor}, 1)`;
      this.chooseColor(colorFromRGB, 'rgb');
    }
  }

  extractRgbaValues(rgba: string) {
    const rgbaValues = rgba.match(/\d+/g);
    // this.redColor = 44;
    this.redColor = parseInt(rgbaValues[0]);
    this.greenColor = parseInt(rgbaValues[1]);
    this.blueColor = parseInt(rgbaValues[2]);
  }

  limitColorValue(value: number): number {
    if (value > 255) {
      return 255;
    } else {
      return value;
    }
  }


  changeHueSlide(data) {
    this.hue = data
    this.hueChangeSlide ++
  }
}

