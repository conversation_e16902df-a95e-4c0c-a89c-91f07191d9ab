<ng-container class="w-full  ">
  <div class="p-6">
    <app-color-palette class="w-full z-100	" [hue]="hue" [colorInit]="color" [hueChangeSlide]="hueChangeSlide"
                       (color)="chooseColor($event, 'palette')"></app-color-palette>
    <div class="flex items-center justify-center">
      <app-color-slide
        (color)="changeHueSlide($event)"
        [colorHue]="hue"
      ></app-color-slide>
    </div>
    <div class=" pb-2 border-b-2 mb-2">
      <div class="flex">
        <div class="mr-2 flex items-center flex-col	">
          <input class="border rounded-md px-4 h-10 border-slate-300	w-full" type="text" [(ngModel)]="hexColor"
                 appHexInput
                 (ngModelChange)="convertHexToRgb()">
          <h1 class="mt-2">HEX</h1>
        </div>
        <div class="mr-2 flex items-center flex-col	">
          <input class="border rounded-md px-4 h-10 border-slate-300 w-16"
                 onlyNumber
                 [(ngModel)]="redColor"
                 (keyup)="inputRGB($event, 'red')"
                 (blur)="inputRGB($event, 'red')"
                 [maxLength]="3"
          >
          <h1 class="mt-2">R</h1>
        </div>
        <div class="mr-2 flex items-center flex-col	">
          <input class="border rounded-md px-4 h-10 border-slate-300 w-16"
                 onlyNumber
                 [(ngModel)]="greenColor"
                 (keyup)="inputRGB($event, 'green')"
                 (blur)="inputRGB($event, 'green')"
                 [maxLength]="3">
          <h1 class="mt-2">G</h1>

        </div>
        <div class="mr-2 flex items-center flex-col	">
          <input class="border rounded-md px-4 h-10 border-slate-300 w-16"
                 onlyNumber
                 [(ngModel)]="blueColor"
                 (keyup)="inputRGB($event, 'blue')"
                 (blur)="inputRGB($event, 'blue')"
                 [maxLength]="3">
          <h1 class="mt-2">B</h1>
        </div>
      </div>
    </div>
    <div>
      <div class="grid grid-rows-4 grid-flow-col gap-2">
        <div *ngFor="let colorItem of colors">
          <div class="w-6 h-6 rounded"
               [style.background-color]="colorItem" (click)="chooseColor(colorItem, 'example')"></div>
        </div>
      </div>
    </div>
    <div class="footer mt-6 flex justify-end">
      <button mat-raised-button class="mr-3" (click)="dialogRef.close()"
      >
        <span>{{'cancel'}}</span>
      </button>
      <button mat-raised-button color="primary" class="mr-3" (click)="makeColor()">
        <span>{{'yes'}}</span>
      </button>
    </div>
  </div>

</ng-container>

