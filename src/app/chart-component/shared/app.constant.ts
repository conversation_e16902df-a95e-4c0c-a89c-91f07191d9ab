export const DATE_FORMAT = 'DD/MM/YYYY';
export const DATE_TIME_FORMAT = 'DD/MM/YYYY HH:mm:ss';

export interface DATE_COUNT {
  years: number;
  months: number;
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  milliseconds: number;
}

export enum ACTION {
  CANCEL = "CANCEL",
  SAVE = "SAVE"
}

export function configSnackBar(type: string) {
  return {
    panelClass:
      type === 'success'
        ? 'bg-lime-500'
        : type === 'warning'
          ? 'bg-yellow-500'
          : 'bg-red-500',
  }
}

export function closeOneCdkBackdropReverse() {
  const backdrops = document.querySelectorAll('.cdk-overlay-backdrop');
  (<HTMLElement>backdrops[backdrops.length - 1]).click();
}

export interface CustomChartConfig {
  chartType: string;
  suffix: string;
  formatType: string;
  name: string;
  value: string;
  uniqueValues: [];
  piePositionX: number,
  piePositionY: number,
  sizePie: number,
  innerSize: number,
  pieIsSum: false,
  drilldownName?: '',
  drilldown?: [],
  dataLabel: boolean,
  isStack: boolean,
  inputText: string
}

export enum TYPE_MESSAGE_SOCKET {
  NEW_MESSAGE = "message/new",
  NEW_MESSAGE_QUERY_CHAT = "message/new_for_query_chat",
  NEW_MESSAGE_CS_CHAT = "message/new_for_cs_chat",
  NEW_MESSAGE_COMPLEX_CS_CHAT = "message/new_complex_for_cs_chat",
  GET_SETTING_AI = "ai/get_setting",
  NEW_MESSAGE_CS_CHAT_PAUSE = "message/cs_chat_pause",
  NEW_MESSAGE_FLOW_START = "message/flow_start",
  SAVE_USER_INFO = "message/save_user_info",
  NEW_MESSAGE_HUMAN_REPLY = 'message/new_by_human_reply',
}

export enum COMPLEX_MESSAGE_TYPE {
  INTERACTIVE = "interactive",
  RICH_CARD = "rich_card",
  MEDIA = "media",
  FILE = "file"
}

export enum MEDIA_MESSAGE_TYPE {
  IMAGE = "image",
  VIDEO = "video",
  AUDIO = "audio"
}
