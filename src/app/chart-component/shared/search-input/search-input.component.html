<mat-form-field class="fuse-mat-no-subscript w-full">
  <mat-label *ngIf="showLabel" [ngClass]="isRequire? 'required': ''">{{ labelName }}</mat-label>
  <mat-select
    [placeholder]="placeholder"
    (valueChange)="select($event)"
    (openedChange)="onOpenedChange(matSelect, $event)"
    #matSelect
    [value]="value"
    [multiple]="multiple"
    [panelClass]="multiple ? 'select-multiple field' : 'select-single field'"
    [matTooltip]="haveTooltip ? (matSelect?.triggerValue ?? '') : null"
    matTooltipClass="cus-tooltip"
    matTooltipPosition="below"
    [disabled]="isDisable"
  >
    <mat-option *ngIf="canBeSearch" class="w-2/3">
      <ngx-mat-select-search
        [(ngModel)]="valueInput"
        [noEntriesFoundLabel]="'hrm-management.contract.notFound' "
        (ngModelChange)="search($event)"
        [placeholderLabel]="labelName">
        >
        <mat-icon
          ngxMatSelectSearchClear
          class="icon-size-5"
          matPrefix
          [title]="'close' "
        ></mat-icon>
      </ngx-mat-select-search>
    </mat-option>

    <mat-checkbox *ngIf="canSelectAll && multiple" class="pl-4 py-2" [checked]="isSelectedAll" (change)="onCheckboxChange()">
      {{!isSelectedAll ? ("searchInput.selectAll" ) : ("searchInput.deselectAll" )}}
    </mat-checkbox>
    <mat-option *ngIf="canBeNull">{{'hrm-management.department.title_none' }}</mat-option>
    <ng-container *ngFor="let item of listOptions">
      <div>
        <mat-option [value]="optionValue ? item[optionValue] : item" *ngIf="item.showSearch === 1 || !item.showSearch">
          <ng-container *ngIf="labelTemplate; else labelTemplateRef">
            <ng-container *ngTemplateOutlet="labelTemplate; context: {item: item}"></ng-container>
          </ng-container>
          <ng-template #labelTemplateRef>
          <span
            [matTooltip]="(haveTooltip ? item[optionLabel] : '') "
            matTooltipClass="cus-tooltip"
            matTooltipPosition="below"
          >
          {{"" + item[optionLabel] }}
        </span>
          </ng-template>
        </mat-option>
      </div>
    </ng-container>
  </mat-select>
</mat-form-field>

<div class="hidden">
  <div #clearDataRef class="clear-data" (click)="clear($event)">
    <svg width="16px" height="16px">
      <use xlink:href="#icon_close"></use>
    </svg>
  </div>
</div>
