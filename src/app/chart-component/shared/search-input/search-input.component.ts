import {
  AfterViewInit, ChangeDetectorRef,
  Component,
  ContentChildren, ElementRef,
  EventEmitter,
  Input, OnChanges,
  OnInit,
  Output,
  QueryList, SimpleChanges,
  TemplateRef, ViewChild
} from '@angular/core';
import {CommonUtilsService} from "@shared/common-utils.service";
import {HrmTemplate} from "@shared/directives/hrm-template.directive";
import {MatSelect} from "@angular/material/select";
import {FormControl} from '@angular/forms';

@Component({
  selector: 'app-search-input',
  templateUrl: './search-input.component.html',
  styleUrls: ['./search-input.component.scss']
})
export class SearchInputComponent implements OnInit, AfterViewInit, OnChanges {
  @ContentChildren(HrmTemplate) templates: QueryList<HrmTemplate>;
  @ViewChild('matSelect') matSelectElm?: MatSelect;
  @ViewChild('clearDataRef') clearDataElm?: ElementRef;

  @Input() multiple: boolean = false;
  @Input() listOptions: any[] = [];
  @Input() value: any;
  valueSet: Set<any> = new Set();
  @Output() valueChange = new EventEmitter();

  optionLabel: string = 'name'; // các giá trị hiện thị
  @Input()
  set setOptionLabel(value: string) {
    if (value == undefined) {
      this.optionLabel = 'name';
    } else {
      this.optionLabel = value;
    }
  }

  keySearch: string = 'name'; // giá trị để tìm kiếm
  @Input()
  set setKeySearch(value: string) {
    if (value == undefined) {
      this.keySearch = 'name';
    } else {
      this.keySearch = value;
    }
  }

  optionValue: string | null = null;

  @Input() // giá trị để truyền dữ liệu
  set setOptionValue(value: string) {
    if (value == undefined) {
      this.optionValue = null;
    } else {
      this.optionValue = value;
    }
  }

  @Input() showLabel: boolean = false; // xem có show label không
  @Input() labelName: any = 'Place holder';
  @Input() haveTooltip: boolean = true;
  @Input() canBeNull: boolean = false;
  @Input() isRequire: boolean = true;
  @Input() placeholder = '';

  canBeSearch: boolean = true;

  @Input()
  set setCanBeSearch(value: boolean) {
    if (value == undefined) {
      this.canBeSearch = true;
    } else {
      this.canBeSearch = value;
    }
  }

  @Input() needToolTip: boolean = false;
  @Input() showClear = false;
  @Input() isDisable: boolean = false;
  valueInput: any = ''
  regexSpace = /\s(?=\s)/g;
  labelTemplate: TemplateRef<any>;
  isSelectedAll: boolean = false;
  @Input() canSelectAll: boolean = false;

  constructor(
    private cdr: ChangeDetectorRef
  ) {
  }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.value) {
      this.updateValueSet();
    }
  }

  private updateValueSet() {
    this.valueSet = new Set();

    if (Array.isArray(this.value)) {
      this.value.forEach(item => {
        this.valueSet.add(item);
      });
    } else {
      this.valueSet.add(this.value);
    }
    this.isSelectedAll = this.areAllValuesInSet();
  }

  search(res: any) {
    if (res != null) {
      this.listOptions.forEach((x) => {
        const key = CommonUtilsService.formatRemoveVietnameseTones(x[this.keySearch])?.replace(this.regexSpace, '').trim().toLowerCase();
        const searchValue = CommonUtilsService.formatRemoveVietnameseTones(res)?.replace(this.regexSpace, '').trim().toLowerCase();

        x.showSearch = (key.includes(searchValue) || searchValue.includes(key)) ? 1 : 2;
      });
      if (this.listOptions.filter(item => item.showSearch === 1 || !item.showSearch).length > 3) {
        this.canSelectAll = true
      } else {
        this.canSelectAll = false
      }
    }

  }

  select(value: any) {
    this.valueChange.emit(value);
  }

  onOpenedChange(select: any, event: any): void {
    if (event) {
      const offsetY = select._offsetY;
      const topset = -offsetY - 11;
      const selectSearchPanel = select.panel.nativeElement as HTMLElement;
      selectSearchPanel.style.top = `${topset}px`;
      selectSearchPanel.style.display = `block`;
    }
  }

  ngAfterViewInit(): void {
    if (this.matSelectElm) {
      if (this.showClear && this.clearDataElm?.nativeElement) {
        const parentClear = this.matSelectElm._elementRef.nativeElement.querySelector('.mat-select-value');
        if (parentClear) {
          parentClear.appendChild(this.clearDataElm.nativeElement);
        }
      }
    }

    (this.templates as QueryList<HrmTemplate>).forEach((item) => {
      switch (item.getType()) {
        case 'label':
          this.labelTemplate = item.template;
          break;
      }
    });
    this.cdr.detectChanges();
  }

  clear(event: MouseEvent) {
    event.stopPropagation();
    if (this.multiple) {
      this.value = new Set();
    } else {
      this.value = null;
    }
    this.valueChange.emit(this.value);
  }

  onCheckboxChange() {
    if (!this.isSelectedAll) {
      this.listOptions.forEach((item) => {
        if (item.showSearch === 1 || !item.showSearch) {
          this.valueSet.add(item[this.optionValue]);
        }
      });
    } else {
      this.listOptions.forEach((item) => {
        if (item.showSearch === 1 || !item.showSearch) {
          this.valueSet.delete(item[this.optionValue]); // Sử dụng `delete` thay vì `add`
        }
      });
    }
    this.valueChange.emit(Array.from(this.valueSet));
  }

  areAllValuesInSet(): boolean {
    // Filter the list based on showSearch criteria
    const filteredList = this.listOptions.filter(item => item.showSearch === 1 || !item.showSearch).map((item) => item[this.optionValue]);

    // Check if all filtered values are in the Set
    return filteredList.every(item => this.valueSet.has(item));
  }
}
