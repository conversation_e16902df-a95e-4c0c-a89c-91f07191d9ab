.tab {
  .tab-item {
    @apply px-4 py-1.5 font-bold text-[#7A868D] cursor-pointer;

    &:hover {
      @apply text-[#0082A8];
    }

    &.active {
      @apply text-[#0082A8] border-b border-b-[#0082A8];
    }
  }
}

.example-box {
  cursor: move;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
  0 8px 10px 1px rgba(0, 0, 0, 0.14),
  0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-box:last-child {
  border: none;
}

.example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.active-class {
  border-color: #5599FF;
  border-width: 2px;
  border-radius: 0.25rem;
}




