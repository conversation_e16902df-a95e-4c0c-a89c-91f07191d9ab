/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable max-len */
import {Injectable, Injector, Inject, ChangeDetectorRef, ViewChild} from '@angular/core';
import {MatSnackBar, MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition} from '@angular/material/snack-bar';
import {take} from 'rxjs/operators';
import {MatDialog, MatDialogRef, MatDialogConfig} from '@angular/material/dialog';
import {FormBuilder, FormGroup} from '@angular/forms';
// import {BaseService} from "@core/base.service";
import {CommonUtilsService} from '@shared/common-utils.service';
import {DomSanitizer} from '@angular/platform-browser';
import {configSnackBar} from "@shared/app.constant";

@Injectable()
export class BaseComponent {
  formGroup: FormGroup;
  searchModel: any = {
    page: 0,
    pageSize: 20,
    status:1
  };
  searchModelNew: any = {
    page: 0,
    pageSize: 20,
    isDeleted: 0
  };
  searchResult: any = {
    data: [],
    totalRecords: 0,
  };
  pageIndex: any;
  listTimeType = ['createdDate', 'modifiedDate', 'expectEndTime', 'actualEndTime', 'dateOfBirth', 'leaveDate', 'staOfficalDate', 'hireDate', 'staDate', 'endDate','effDate','signDate','expDate', 'dateOfIssue', 'expiredDate', 'startDate', 'timeFrom', 'timeTo', 'date'];
  horizontalPosition: MatSnackBarHorizontalPosition = 'end';
  verticalPosition: MatSnackBarVerticalPosition = 'top';
  public snackBar: MatSnackBar;
  public cdr: ChangeDetectorRef;
  public dialogService: MatDialog;
  public _sanitizer: DomSanitizer;
  public fb: FormBuilder;
  // private baseService: BaseService;
  public dialogRef: MatDialogRef<any>;
  public detailsData: any;

  constructor(
    injector: Injector,
    // service?: BaseService,
    dialogRef?: MatDialogRef<any>,
    // service1?: BaseService,
  ) {
    this.snackBar = injector.get(MatSnackBar);
    this.cdr = injector.get(ChangeDetectorRef);
    this.dialogService = injector.get(MatDialog);
    this._sanitizer = injector.get(DomSanitizer);
    this.fb = injector.get(FormBuilder);
    // this.baseService = service;
    this.dialogRef = dialogRef;
  }

  showSnackBar(messages?: string, type?: string): void {
    console.log(this.snackBar)
    this.snackBar.open(messages, null, {
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: 2000,
      panelClass:
        type === 'success'
          ? 'bg-lime-500'
          : type === 'warning'
            ? 'bg-yellow-500'
            : 'bg-red-500',
    })
  }

  showDialog(
    component?: any,
    options: MatDialogConfig = {},
    callback?: any
  ): any {
    const ref = this.dialogService.open(component, {
      width: '30vw',
      ...options,
    });
    ref
      .afterClosed()
      .pipe(take(1))
      .subscribe((value) => {
        callback && callback(value);
      });
    return ref;
  }

  closeDial0g(): void {
    this.dialogService.closeAll();
  }

  handleCoverTimeToString(data): void {
      this.listTimeType.forEach((item) => {
        if (data[item]) {
          data[item] = CommonUtilsService.dateToString(data[item]);
        }
      });
  }

  handleCoverStringToDate(data): void {
    this.listTimeType.forEach((item) => {
      if (data[item]) {
        data[item] = CommonUtilsService.stringToDate(
          CommonUtilsService.dateToString(data[item])
        );
      }
    });
  }

  getDetails(id, callback?): void {
    // this.baseService.getOne(id).subscribe((res) => {
    //   if (res.code === '00') {
    //     this.detailsData = res.data;
    //     this.handleCoverStringToDate(this.detailsData);
    //     if (this.formGroup) {
    //       this.formGroup.patchValue(this.detailsData);
    //       this.formGroup.markAllAsTouched();
    //     }
    //     if (callback) {
    //       callback(this.detailsData);
    //     }
    //
    //   } else {
    //     this.dialogService.closeAll();
    //   }
    // });
  }

  extractUrls(text: string, role?: string): string {
    if (!text) return '';
    text = text.split(/\s*<dx_text_source>.*<\/dx_text_source>/)[0];
    const urlRegex = /(https?:\/\/[^\s()]+)/g;
    const phoneRegex = /(\(\+\d{1,4}\)|0)((\s?((\d{2,}\s?\d{3,}\s?\d{3,})|(\d{7,})))|(\.?(\d{2,}\.?\d{3,}\.?\d{3,}))|(-?(\d{2,}-?\d{3,}-?\d{3,})))/g;
    const mailRegex = /[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}/g;
    const imageTagRegex = /<img\s+[^>]*src="([^"]*)"[^>]*>/g;
    const imgTagList = (text.match(imageTagRegex) || []).map(v => v.replace(/<img/g, `<img id="imgCheck" class="rounded-md zoom"`));

    const aTagList = text.replace(urlRegex, '<a href="$&" class="text-light-primary hover:underline" target="_blank">$&</a>').split(/(<[^>]*>)/g);
    const urlList = [];
    for (let textSplited of aTagList) {
      if (textSplited.includes("<a")) {
        urlList.push(CommonUtilsService.extractTextDoubleQuotes(textSplited)[0]);
      }
    }

    return text
      .replace(imageTagRegex, '%image')
      .replace(urlRegex, `<a href="$&" class="${role === 'user' ? 'text-white' : 'text-light-primary'} hover:underline whitespace-pre-wrap" target="_blank">$&</a>`)
      .replace(phoneRegex, (match) => {
        if (urlList.some(url => url.includes(match))) return match;
        return `<a href="tel:${match}" class="${role === 'user' ? 'text-white' : 'text-light-primary'} hover:underline whitespace-pre-wrap" target="_blank">${match}</a>`;
      })
      .replace(mailRegex, `<a href="mailto:$&" class="${role === 'user' ? 'text-white' : 'text-light-primary'} hover:underline whitespace-pre-wrap" target="_blank">$&</a>`)
      .replace(/%image/g, () => {
        return imgTagList.length > 0 ? imgTagList.shift() : '';
      });
  }

  classifyMessage(input) {
    const trimmedInput = input.trim(); // Loại bỏ khoảng trắng và ký tự xuống dòng ở đầu và cuối chuỗi
    if (trimmedInput.startsWith("<dx_gll>")) {
      return "gallery";
    } else if (trimmedInput.startsWith("<dx_gr>")) {
      return "grid";
    } else {
      return "text"
    }
  }

  extractImages(input) {
    const trimmedInput = input.trim();
    let content = "";

    if (!trimmedInput.startsWith("<dx_gll>") || !trimmedInput.endsWith("</dx_gll>")) {
      content = trimmedInput.slice(7, -8).trim();
    } else {
      content = trimmedInput.slice(8, -9).trim();
    }

    // Tách các dòng và lọc bỏ các dòng rỗng
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);

    const images = lines.map(line => {
      const parts = line.split('", "');
      const src = parts[0].replace(/"/g, '');
      const label = parts[1] ? parts[1].replace(/"/g, '') : '';
      return { src, label };
    });

    return images;
  }

  extractSources(input) {
    const trimmedInput = input.trim();
    let content = "";

    const regex = /<dx_text_source>(.*?)<\/dx_text_source>/;
    const match = trimmedInput.match(regex);

    if (match) {
      content = match[1]
    }

    const lines = content.split(',').map(line => line.trim()).filter(line => line);

    // Kết hợp các phần tử thành đối tượng
    const result = [];
    for (let i = 0; i < lines.length; i += 3) {
      // Kiểm tra để tránh lỗi khi số phần tử không chia hết cho 3
      if (i + 2 < lines.length) {
        const source = lines[i].replace(/"/g, ''); // Loại bỏ dấu ngoặc kép
        const title = lines[i + 1].replace(/"/g, '');
        const type = lines[i + 2].replace(/"/g, '');
        result.push({ source, title, type });
      }
    }
    return result;
  }
}
