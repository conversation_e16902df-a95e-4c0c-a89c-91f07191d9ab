import {ChangeDetectorRef, Component, ElementRef, Inject, Injector, OnInit, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {DateAdapter} from "@angular/material/core";
import {BaseComponent} from "@core/base.component";
import {cloneDeep, random} from "lodash-es";
import {v4 as uuidv4} from 'uuid';
import {CdkDragDrop, moveItemInArray} from '@angular/cdk/drag-drop';
import defaultColor from "../shared/models/general.model";
@Component({
  selector: 'app-add-or-edit-chart',
  templateUrl: './add-or-edit-chart.component.html',
  styleUrls: ['./add-or-edit-chart.component.scss']
})
export class AddOrEditChartComponent extends BaseComponent implements OnInit {

  tabs = [
    {
      type: 'QUERY',
      name: 'Query',
    },
    {
      type: 'TRANSFORM',
      name: 'Transform',
    }
  ];
  currentTab = 'QUERY';

  legendInput: string [] = [];
  listSort = [{
    name: "DEFAULT",
    label: "Mặc định"
  }, {
    name: "INCREASE",
    label: "Tăng dần"
  }, {
    name: "DECREASE",
    label: "Giảm dần"
  }]

  config: any = {
    nameChart: '',
    nameYAxis: '',
    xAxis: null,
    category: 'OT',
    series: [],
    shared: false,
    dataLabel: false,
    YAxisMax: null,
    typeData: '',
  };

  configChart = {
    nameChart: '',
    nameYAxis: '',
    xAxis: null,
    category: 'OT',
    series: [],
    shared: false,
    dataLabel: false,
    YAxisMax: null,
    typeData: '',
  }

  keysArray = [];
  dialogId: any = null;

  ListCategories = [{
    name: 'FI',
    label: "FI"
  }, {
    name: 'HR',
    label: "HR"
  }, {
    name: 'OT',
    label: "OT"
  }]

  editorChart = [
    {
      name: "Data",
      id: 1
    },
    {
      name: "Configuartion",
      id: 2
    }
  ];
  listChartType = [{
    name: 'column'
  }, {
    name: 'line'
  }, {
    name: 'pie'
  }, {
    name: 'bar'
  }]

  listFormat = [{
    name: "Default",
    code: "Default"
  }, {
    name: "currency",
    code: "currency"
  }, {
    name: "number",
    code: "number"
  }]

  visibleContents: number[] = []; // Sử dụng mảng để lưu trữ các nội dung đang hiển thị
  sqlStr: any;
  previousPieChart: number = 0
  chartId: any = null

  activeTab = 0; // Initialize with the index of the default active tab

  colors = defaultColor;
  private listWidget: any;

  constructor(
    injector: Injector,
    public dialogRef: MatDialogRef<AddOrEditChartComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _adapter: DateAdapter<any>,
  ) {
    super(injector, dialogRef);
    this.data = data?.data;
    this.sqlStr =  JSON.stringify(data?.data)
    this.config = data?.config
    this.getData()
    this.updateConfig()

  }

  ngOnInit(): void {
    this.searchModel = {isDeleted: 0, pageSize: 99999, page: 0, keyword:''}
    this.removeAlreadyColor()
    // this.getListWidget();
  }

  // remove already color
  removeAlreadyColor(){
    this.config.series.forEach(serie => {
      serie.uniqueValues.forEach(unique => {
        const indexRemoveColor = this.colors.indexOf(unique.color);
        if(indexRemoveColor !== -1){
          this.colors.splice(indexRemoveColor, 1);
        }
      })
    })
  }

  // getListWidget(){
  //   const searchModel = {isDeleted: 0, pageSize: 99999, page: 0, keyword:''}
  //   this.processSearch(searchModel);
  //   this.listWidget = this.searchResult
  //   console.log(this.listWidget)
  //
  // }

  updateConfig() {
    //handle uniqueValue
    this.config.series.forEach(series => {
      let uniqueValues = []
      switch (series.inputText) {
        case "GROUP":
          uniqueValues = [...new Set(this.data.map(item => item[series.name]))];
          break;
        case "SUM":
          uniqueValues = [undefined];
        default:
          break;
      }
      const array1 = uniqueValues;
      const array2 = series.uniqueValues
      if (!array2 || !array1.some(value => array2.map(item => item.name).includes(value))) {
        series.uniqueValues = uniqueValues.map((value) => ({
          name: value,
          idSeries: uuidv4(),
          color: this.chooseRandomColor(),
          dataLabel: true,
        }));
      }
    });

    //handle x position pie
    const numberPieChar = this.config.series.filter(series => series.chartType === 'pie').length;
    if (numberPieChar != this.previousPieChart) {
      let xDefault = 750 - 150 * numberPieChar
      this.config.series.forEach(serie => {
        if (serie.chartType === 'pie') {
          serie.piePositionX = xDefault;
          xDefault += 300
        }
      })
    }
    this.previousPieChart = numberPieChar
    this.configChart = cloneDeep(this.config)
  }

  // handle drilldown

  updateDrilldown(serie: any) {
    serie.uniqueValues.forEach(uniqueValue => {
      const dataFilter = this.data.filter(item => item[serie.name] == uniqueValue.name )
      const uniqueDrilldown =  [...new Set(dataFilter.map(item => item[serie.drilldownName]))];
      const array1 = uniqueDrilldown;
      const array2 = serie.uniqueValues.drilldown
      if (!array2 || !array1.some(value => array2.map(item => item.name).includes(value))) {
        uniqueValue.drilldown = uniqueDrilldown.map((value) => ({
          name: value,
          idSeries: uuidv4(),
          color: this.colors[random(29)],
          dataLabel: true,
        }));
      }
    })
    this.configChart = cloneDeep(this.config)
  }

  createNewSeries() {
    this.config.series.push({
      chartType: null,
      name: null,
      value: null,
      formatType: null,
      suffix: null,
      inputText: 'GROUP',
      piePositionX: 0,
      piePositionY: 150,
      sizePie: 200,
      innerSize: 0,
      pieIsSum: false,
      drilldownName: '',
      drilldown: [],
      dataLabel: false,
      isStack: false,
      sortBy: "DEFAULT"
    })
  }

  isContentVisible(id) {
    // Kiểm tra xem id có trong mảng visibleContents không
    return this.visibleContents.includes(id);
  }

  changeTabs(type) {
    this.currentTab = type
  }

  getData() {
    // this.data = this.sqlStr
    const convertedData = this.data.map(item => {
      for (const key in item) {
        if (!isNaN(Number(item[key]))) {
          item[key] = Number(item[key]);
        }
      }
      return item;
    });
    this.keysArray = Object.keys(convertedData[0]).map(key => ({
      name: key,
      type: typeof convertedData[0][key],
      value: key
    }));
    this.updateConfig()
  }

  updateSeries(serie) {
    const stringFields = this.keysArray.filter(item => item.type === 'string');
    const numberFields = this.keysArray.filter(item => item.type === 'number')
    if (!serie.value && numberFields.length > 0) {
      serie.value = numberFields[0].name;
    }
    if (!serie.name && stringFields.length > 1) {
      serie.name = stringFields[1].name;
    } else {
      serie.name = numberFields[0].name;
      serie.inputText = 'SUM'
    }
    if (!serie.formatType) {
      serie.formatType = this.listFormat[0].code
    }
    if (!this.config.xAxis && stringFields.length > 0) {
      this.config.xAxis = stringFields[0].name;
    }
    this.updateConfig()
  }

  deleteOptional(index) {
    this.config.series.splice(index, 1)
    this.updateConfig()
  }

  changeColor(data: any) {
    this.config.series.forEach(serie => {
      if (serie.uniqueValues)
        serie.uniqueValues.forEach(unique => {
          if (unique.idSeries == data.idSeries) {
            unique.color = data.color
          }
        })
    })
    this.configChart = cloneDeep(this.config)
  }

  save() {
    const convertedData = {
      id: this.chartId ?? null,
      config: JSON.stringify({
          config: this.config,
          dataChart: {data: JSON.parse(this.sqlStr)}
        }
      ),
      category: this.config.category,
      nameChart: this.config.nameChart,
    }
/*    if (this.dialogId) {
      this.chartService.updateCustomChart(convertedData).subscribe(res => {
        if (res.code == '00') {
          this.dialogRef.close()
        }
      })
    } else {
      this.chartService.createCustomChart(convertedData).subscribe(res => {
        if (res.code == '00') {
          this.dialogRef.close()
        }
      })
    }*/
  }

  updateStack(index: number) {
    this.config.series[index].uniqueValues.forEach(uniqueValue => {
      uniqueValue.stack = this.config.series[index].isStack ? index + 1 : null;
    });
    this.configChart = cloneDeep(this.config)
  }

  updateDataLabel(index, event ?: any) {
    if (event) {
      this.config.series[index].listLabel = event
      this.config.series[index].uniqueValues.forEach(uniqueValue => {
        uniqueValue.dataLabel = event.includes(uniqueValue.name)
      });
    } else {
      this.config.series[index].listLabel = this.getDataLabel(index)
    }
    this.configChart = cloneDeep(this.config)
  }

  getDataLabel(index) {
    const data = [];
    this.config.series[index].uniqueValues.forEach(uniqueValue => {
      if (uniqueValue.dataLabel === true) {
        data.push(uniqueValue.name);
      }
    });
    return data;
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.config.series, event.previousIndex, event.currentIndex);
  }

  onTabChanged(event: number) {
    this.activeTab = event; // Update the active tab index
  }

  changeNameChart($event: string) {
    this.config.nameChart = $event
    this.configChart = cloneDeep(this.config)
  }

  chooseTypeChart(type){
    this.config.series[0].chartType = type;
    this.configChart = cloneDeep(this.config)
  }

  chooseRandomColor(){
    const colorLeft = this.colors.length;
    const randomIndex = random(colorLeft - 1);
    const colorPicked = this.colors[randomIndex];
    this.colors.splice(randomIndex, 1);
    if (this.colors.length === 0){
      this.colors = defaultColor;
    }
    return colorPicked
  }
}
