import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { io, Socket } from 'socket.io-client';
import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SocketService {
  public connectedSocketSuccessCs$: BehaviorSubject<string|null> = new BehaviorSubject<string | null>(null);
  public connectedSocketSuccessMessage$: BehaviorSubject<string|null> = new BehaviorSubject<string | null>(null);
  public connectedFlagCs$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public connectedFlagMessage$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  socketCs: Socket;
  socketMessage: Socket;
  ai_id: string = null;
  domain: string = '';
  intervalRetryCs: any;
  intervalRetryMessage: any;

  connectSocketCs({ ai_id, domain, user }) {
    if (this.connectedSocketSuccessCs$.value === "success") return;
    const options = {
      query: {
        ai_id,
        domain,
        user: user ? JSON.stringify(user) : '',
      }
    };
    this.ai_id = ai_id;
    this.socketCs = io(environment.SOCKET_CS_ENDPOINT, options);
    this.socketCs.on('connect', () => {
      if (this.intervalRetryCs) clearInterval(this.intervalRetryCs);
      this.connectedSocketSuccessCs$.next("success");
    });

    this.socketCs.on('connect_error', (error) => {
      if (this.intervalRetryCs) clearInterval(this.intervalRetryCs);
      this.retryConnectCs()
    });

    this.socketCs.on('disconnect', () => {
      this.connectedSocketSuccessCs$.next("disconnect");
    });
  }

  connectSocketMessage({ ai_id, domain, user }) {
    if (this.connectedSocketSuccessMessage$.value === "success") return;
    const options = {
      query: {
        ai_id,
        domain,
        user: user ? JSON.stringify(user) : '',
      }
    };
    this.ai_id = ai_id;
    this.socketMessage = io(environment.SOCKET_MESSAGE_ENDPOINT, options);
    this.socketMessage.on('connect', () => {
      if (this.intervalRetryMessage) clearInterval(this.intervalRetryMessage);
      this.connectedSocketSuccessMessage$.next("success");
    });

    this.socketMessage.on('connect_error', (error) => {
      if (this.intervalRetryMessage) clearInterval(this.intervalRetryMessage);
      this.retryConnectMessage()
    });

    this.socketMessage.on('disconnect', () => {
      this.connectedSocketSuccessMessage$.next("disconnect");
    });
  }

  joinRoomConversationCs(message: any) {
    if (this.socketCs && this.connectedSocketSuccessCs$.value === 'success') {
      this.connectedFlagCs$.next(false);
      this.socketCs.emit('join_room_conversation', message);
    }
  }

  joinRoomConversationMessage(message: any) {
    if (this.socketMessage && this.connectedSocketSuccessMessage$.value === 'success') {
      this.connectedFlagMessage$.next(false);
      this.socketMessage.emit('join_room_conversation', message);
    }
  }

  callStartFlow(message: any) {
    if (this.socketCs && this.connectedSocketSuccessCs$.value === 'success') {
      this.socketCs.emit('call_start_flow', message);
    }
  }

  sendMessage(message: any, conversationId: string) {
    if (this.socketCs && this.connectedSocketSuccessCs$.value === 'success') {
      this.socketCs.emit('query_chat_message', message, { conversationId: conversationId});
    }
  }

  callSettingDefault(conversationId) {
    if (this.socketCs && this.connectedSocketSuccessCs$.value === 'success') {
      this.socketCs.emit('call_setting_default', { conversationId: conversationId});
    }
  }

  listenEventCs(event: string) {
    if (this.socketCs) {
      return new Observable((subscriber) => {
        this.socketCs.on(event, (data) => {
          subscriber.next(data);
        });
      });
    } else {
      return new Observable();
    }
  }

  listenEventMessage(event: string) {
    if (this.socketMessage) {
      return new Observable((subscriber) => {
        this.socketMessage.on(event, (data) => {
          subscriber.next(data);
        });
      });
    } else {
      return new Observable();
    }
  }

  disconnectCs() {
    if (this.socketCs) {
      this.ai_id = null;
      this.socketCs.disconnect();
      this.connectedSocketSuccessCs$.next(null);
    }
  }

  disconnectMessage() {
    if (this.socketMessage) {
      this.ai_id = null;
      this.socketMessage.disconnect();
      this.connectedSocketSuccessMessage$.next(null);
    }
  }

  private retryConnectCs() {
    this.intervalRetryCs = setInterval(() => {
      this.socketCs.connect()
    }, 1000)
  }

  private retryConnectMessage() {
    this.intervalRetryMessage = setInterval(() => {
      this.socketMessage.connect()
    }, 1000)
  }
}
