import {Injectable, OnD<PERSON>roy} from '@angular/core';
import {BehaviorSubject, Observable, of, ReplaySubject, Subject, switchMap, takeUntil} from "rxjs";
import {HttpClient} from "@angular/common/http";
import {environment} from "../../../environments/environment";
import {Router} from "@angular/router";

@Injectable({
  providedIn: 'root'
})
export class LogInService  {

  private account = new BehaviorSubject<any>(null);
  account$ = this.account.asObservable();

  set accessToken(token: string) {
    localStorage.setItem('accessToken', token);
  }

  get accessToken(): string {
    return localStorage.getItem('accessToken') ??  sessionStorage.getItem('accessToken') ?? '';
  }
  private loginName = new BehaviorSubject<string>(''); // Dữ liệu khởi tạo là chuỗi rỗng
  public data$ = this.loginName.asObservable();

  constructor(private _httpClient: HttpClient,
              private router: Router
  ) {
  }


  sendLoginName(acc: string) {
    this.loginName.next(acc);
  }


  signIn(account) :Observable<any>{
    return this._httpClient.post(`${environment.SOCKET_ENDPOINT}/api/user/authenticate`, account)
  }

  signOut() :Observable<any> {
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/auth/sign-in']);
    return of(true);
  }

  setPosition(account: any) {
    this.account.next(account);
  }

  getAccount() {
    return this._httpClient.get(`${environment.SOCKET_ENDPOINT}/api/user/account`);
  }

  updateUserInfoInStorage(user: any, isRemember: boolean) {
    if (isRemember) {
      localStorage.setItem('fullName', user.fullName);
      localStorage.setItem('login', user.login);
      localStorage.setItem('position', user.position);

    } else {
      sessionStorage.setItem('fullName', user.fullName);
      sessionStorage.setItem('login', user.login);
      sessionStorage.setItem('position', user.position);
    }
    this.setPosition(user);
  }

}
