import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ImageZoomService {
  private renderer: Renderer2;
  private overlay: HTMLElement;
  private zoomedImageContainer: HTMLElement;
  private zoomedImage: HTMLElement;
  private altTextElement: HTMLElement;
  private container: HTMLElement;

  constructor(rendererFactory: RendererFactory2) {
    this.renderer = rendererFactory.createRenderer(null, null);
    this.initZoomElements();
  }

  private initZoomElements() {
    // Create container element
    this.container = this.renderer.createElement('div');
    this.renderer.addClass(this.container, 'image-container');

    // Create overlay element
    this.overlay = this.renderer.createElement('div');
    this.renderer.addClass(this.overlay, 'overlay');
    this.renderer.listen(this.overlay, 'click', () => this.closeZoom());

    // Create zoomed image container element
    this.zoomedImageContainer = this.renderer.createElement('div');
    this.renderer.addClass(this.zoomedImageContainer, 'zoomed-image-container');

    // Create zoomed image element
    this.zoomedImage = this.renderer.createElement('img');
    this.renderer.addClass(this.zoomedImage, 'zoomed-image');

    // Create alt text element
    this.altTextElement = this.renderer.createElement('div');
    this.renderer.addClass(this.altTextElement, 'alt-text');

    // Append zoomed image and alt text to zoomed image container
    this.renderer.appendChild(this.zoomedImageContainer, this.zoomedImage);
    this.renderer.appendChild(this.zoomedImageContainer, this.altTextElement);

    // Append zoomed image container to overlay
    this.renderer.appendChild(this.overlay, this.zoomedImageContainer);
    this.renderer.appendChild(this.container, this.overlay);
    this.renderer.appendChild(document.body, this.container);
  }

  init() {
    // Set an interval to check for new images every 5 seconds
    setInterval(() => {
      this.addZoomListeners();
    }, 1000);
  }

  private addZoomListeners() {
    document.querySelectorAll('img.zoom').forEach(image => {
      if (!image.classList.contains('zoom-listener-added')) {
        this.renderer.listen(image, 'click', (event) => this.zoomImage(event));
        this.renderer.addClass(image, 'zoom-listener-added');
      }
    });
  }

  private zoomImage(event: Event) {
    const target = event.target as HTMLImageElement;
    this.renderer.setAttribute(this.zoomedImage, 'src', target.src);
    const message = {
      action: 'zoomImage',
      src: target.src,      // Đường dẫn ảnh
      alt: target.alt || '' // Văn bản thay thế (nếu có)
    }
    console.log(message);
    window.parent.postMessage(message, '*'); // Sử dụng '*' để cho phép gửi thông điệp tới bất kỳ nguồn nào

    // Ẩn đi phần zoom trong iframe (nếu cần)
    this.renderer.setStyle(this.container, 'display', 'none');

  }

  private closeZoom() {
    this.renderer.setStyle(this.container, 'display', 'none');
  }
}
