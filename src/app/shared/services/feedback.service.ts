import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";
import {environment} from "../../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class FeedbackService {

  constructor(private _httpClient: HttpClient,
  ) {
  }

  getListFeedback(param) :Observable<any>{
    return this._httpClient.get(`${environment.SOCKET_ENDPOINT}/api/feedback`, {params : param})
  }

  createFeedback(data) :Observable<any>{
    return this._httpClient.post(`${environment.SOCKET_ENDPOINT}/api/feedback`, data)
  }

  resolvedFeedback(id): Observable<any>{
    return this._httpClient.put(`${environment.SOCKET_ENDPOINT}/api/feedback/${id}/resolve`, null)
  }

}
