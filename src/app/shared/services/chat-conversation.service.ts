import { Injectable } from '@angular/core';
import {BehaviorSubject, Observable} from "rxjs";
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {environment} from "../../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class ChatConversationService {
  private conversationSubject = new BehaviorSubject<string[]>([]);
  public conversation$ = this.conversationSubject.asObservable();
  constructor(private http: HttpClient) { }

  sendMessage(message: any, conversationID: string): Observable<any> {
    const serverAddress = environment.CHAT_ENDPOINT;

    const headers = new HttpHeaders({
      'X-API-Key': 'token'
    });

    // G<PERSON>i yêu cầu tới serverAddress thay vì this.api
    const url = `${serverAddress}/chat/${conversationID}`;

    const payload = {
      role: 'user',
      content: message
    };

    return this.http.post(url, payload, { headers });
  }

  reCall(conversationID: string): Observable<any>{
    const params = { top_recent: 5 };
    const serverAddress = environment.CHAT_ENDPOINT;

    const headers = new HttpHeaders({
      'X-API-Key': 'token'
    });

    // Send the request to serverAddress instead of this.api
    const url = `${serverAddress}/chat/${conversationID}`;

    // Pass headers as part of the options object
    const options = { params, headers };

    return this.http.get(url, options);
  }


  clearConversation(conversation: string[]) {
    this.conversationSubject.next(conversation);
  }
}
