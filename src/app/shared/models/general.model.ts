const defaultColor = [
  "rgba(255, 92, 118, 1)",    // H<PERSON>ng phấn (đậm hơn)
  "rgba(33, 150, 243, 1)",    // Xanh pastel (đậm hơn)
  "rgba(255, 255, 0, 1)",     // <PERSON><PERSON><PERSON> chanh
  "rgba(255, 0, 80, 1)",      // Đỏ anh đào (đậm hơn)
  "rgba(118, 242, 118, 1)",   // Xanh mint (đậm hơn)
  "rgba(200, 200, 250, 1)",   // <PERSON><PERSON><PERSON> (đậm hơn)
  "rgba(255, 95, 0, 1)",      // <PERSON> (đậm hơn)
  "rgba(70, 65, 60, 1)",      // <PERSON><PERSON><PERSON> tro (đậm hơn)
  "rgba(0, 105, 148, 1)",     // <PERSON>anh dư<PERSON>ng biển
  "rgba(92, 64, 51, 1)",      // <PERSON><PERSON>u cappuccino
  "rgba(255, 253, 208, 1)",   // <PERSON><PERSON><PERSON> kem
  "rgba(128, 128, 128, 1)",   // <PERSON><PERSON><PERSON>an<PERSON>
  "rgba(0, 128, 128, 1)",     // <PERSON><PERSON><PERSON> ng<PERSON>
  "rgba(255, 0, 0, 1)",       // Đỏ t<PERSON>i
  "rgba(255, 20, 147, 1)",    // Hồng đào
  "rgba(255, 223, 0, 1)",     // Vàng nắng
  "rgba(0, 128, 0, 1)",       // Xanh l<PERSON> cây
  "rgba(49, 46, 129, 1)",     // Tím than
  "rgba(0, 0, 0, 1)",         // Đen đen
  "rgba(255, 69, 0, 1)",      // Cam nóng
  "rgba(0, 71, 171, 1)",      // Xanh dương cobalt
  "rgba(255, 105, 180, 1)",   // Hồng phớt
  "rgba(139, 69, 19, 1)",     // Nâu socola
  "rgba(211, 211, 211, 1)",   // Xám sáng
  "rgba(218, 165, 32, 1)",    // Vàng hạt dẻ
  "rgba(0, 255, 255, 1)",     // Xanh aqua
  "rgba(139, 0, 0, 1)",       // Đỏ thẫm
  "rgba(148, 0, 211, 1)",     // Tím đậm
  "rgba(154, 185, 115, 1)",   // Xanh olivine
  "rgba(218, 112, 214, 1)"    // Hồng tím
]

export default defaultColor
