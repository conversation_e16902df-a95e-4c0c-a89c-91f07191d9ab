import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dateFormatter'
})
export class DateFormatterPipe implements PipeTransform {

  transform(value: string | Date): string {
    if (!value) return '';

    const date = new Date(value);
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'short', // Thu (viết tắt)
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit', // Thêm giây
      hour12: true, // Định dạng 12 giờ AM/PM
    };

    return new Intl.DateTimeFormat('en-US', options).format(date);
  }

}
