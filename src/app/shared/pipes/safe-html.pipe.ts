import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'safeHtml'
})
export class SafeHtmlPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(value: string): SafeHtml {
    if (!value) return '';

    // Log giá trị đầu vào để debug
    console.log('Input to safeHtml pipe:', JSON.stringify(value));

    // Bước 1: Chuẩn hóa dữ liệu đầu vào
    // Thay thế các dấu xuống dòng \n thành dấu xuống dòng thực
    let processed = value.replace(/\\n/g, '\n');

    // Bước 2: Xử lý ảnh trước khi xử lý link
    processed = processed.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1" class="rounded-xl zoom">');

    // Bước 3: Xử lý link (phải xử lý sau ảnh để tránh nhầm lẫn)
    const linkMatches = processed.match(/\[(.*?)\]\((.*?)\)/g) || [];
    for (const match of linkMatches) {
      // Kiểm tra nếu không phải là ảnh (không có dấu ! ở đầu)
      if (!match.startsWith('!')) {
        const text = match.match(/\[(.*?)\]/)[1];
        const url = match.match(/\((.*?)\)/)[1];
        processed = processed.replace(match, `<a href="${url}" target="_blank">${text}</a>`);
      }
    }

    // Bước 4: Xử lý các tiêu đề trước khi xử lý xuống dòng
    processed = processed.replace(/^\s*#{6}\s+(.+)$/gm, '<h6>$1</h6>');
    processed = processed.replace(/^\s*#{5}\s+(.+)$/gm, '<h5>$1</h5>');
    processed = processed.replace(/^\s*#{4}\s+(.+)$/gm, '<h4>$1</h4>');
    processed = processed.replace(/^\s*#{3}\s+(.+)$/gm, '<h3>$1</h3>');
    processed = processed.replace(/^\s*#{2}\s+(.+)$/gm, '<h2>$1</h2>');
    processed = processed.replace(/^\s*#{1}\s+(.+)$/gm, '<h1>$1</h1>');

    // Bước 5: Xử lý các danh sách
    processed = processed.replace(/^\s*[-*]\s+(.+)$/gm, '<li>$1</li>');
    processed = processed.replace(/^\s*\d+\.\s+(.+)$/gm, '<li>$1</li>');

    // Bước 6: Xử lý các định dạng text
    // Xử lý vừa in đậm vừa in nghiêng (***text***) trước
    processed = processed.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>');
    // Sau đó mới xử lý in đậm và in nghiêng riêng lẻ
    processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    processed = processed.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Bước 7: Xử lý code
    processed = processed.replace(/`([^`]+)`/g, '<code>$1</code>');
    processed = processed.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

    // Bước 8: Cuối cùng mới xử lý xuống dòng
    processed = processed.replace(/\n/g, '<br>');
    const tableRegex = /^\|(.+)\|$/g;  // Matches Markdown table rows
    const lines = processed.split('<br>');  // Split the content by <br> (line breaks)
    let tableHtml = '';
    let isTable = false;
    let tableRows = [];
    let finalProcessed = '';

// Iterate through lines to detect and process table structure
    for (const line of lines) {
      if (line.match(tableRegex)) {
        console.log(line.match(/^[-|]{3,}$/))
        if (line.match(/^[-|]{3,}$/)) {
          continue;
        }
        // If a table row is detected, add it to the current table rows
        if (!isTable) {
          isTable = true;
          tableHtml = '<table border="1" style="border-collapse: collapse;">'; // Add border and border-collapse for styling
        }

        // Process each cell in the row (excluding the first and last column which are empty)
        const cells = line.split('|').slice(1, -1).map(cell => `<td style="padding: 8px; border: 1px solid #ddd;">${cell.trim()}</td>`).join('');
        tableRows.push(cells);
      } else {
        if (isTable) {
          // If we exit the table, close the table body and finish the table
          tableHtml += '<thead><tr>' + tableRows.map(row => `<tr>${row}</tr>`).join('') + '</tr></thead><tbody>';
          tableHtml += '</tbody></table>';
          finalProcessed += tableHtml;  // Add the current table to the final processed content
          tableHtml = '';  // Reset tableHtml for the next table
          isTable = false;
          tableRows = [];
        }
        else {
          // Add regular content to finalProcessed
          finalProcessed += line + '<br>';
        }
      }
    }

// Check if the table was left unclosed and finalize it
    if (isTable) {
      tableHtml += tableRows.map(row => `<tr>${row}</tr>`).join('');
      tableHtml += '</tbody></table>';
      finalProcessed += tableHtml;  // Add the last table to the final processed content
    }

// Replace the table Markdown with the actual HTML table
    processed = finalProcessed;

    return this.sanitizer.bypassSecurityTrustHtml(processed);
  }
}
