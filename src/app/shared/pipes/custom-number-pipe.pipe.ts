import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'customNumberPipe'
})
export class CustomNumberPipePipe implements PipeTransform {

  transform(value: any): any {
    if (typeof value === 'number') {
      // Use the built-in toLocaleString method to add the thousand separator
      return value.toLocaleString();
    } else {
      return value; // Return the input as is if it's not a number
    }
  }

}
