import {Directive, ElementRef, EventEmitter, Output} from '@angular/core';

@Directive({
  selector: '[clickOutside]'
})
export class ClickOutsideDirective {

  @Output('clickOutside') clickOutside: EventEmitter<any> = new EventEmitter();

  constructor(private _elementRef: ElementRef) {
    setTimeout(() => document.addEventListener('click', (e) => this.clickOutsideFn(e)), 1);
    setTimeout(() => document.addEventListener('contextmenu', (e) => this.clickOutsideFn(e)), 1);
  }

  ngOnDestroy(): void {
    document.removeEventListener('click', (e) => this.clickOutsideFn(e));
  }

  private clickOutsideFn(event: MouseEvent): void {
    const targetElement = event.target;
    const clickedInside = this._elementRef.nativeElement.contains(targetElement);
    if (!clickedInside) {
      this.clickOutside.emit();
    }
  }

}
