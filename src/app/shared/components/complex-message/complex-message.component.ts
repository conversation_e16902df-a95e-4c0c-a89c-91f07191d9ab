import { Component, EventEmitter, Input, Output } from '@angular/core';
import { COMPLEX_MESSAGE_TYPE } from '@shared/app.constant';

@Component({
  selector: 'app-complex-message',
  templateUrl: './complex-message.component.html',
  styleUrls: ['./complex-message.component.scss']
})
export class ComplexMessageComponent {
  @Input() type: string;
  @Input() content: any;

  @Output() onActionClick: EventEmitter<any> = new EventEmitter<any>();

  protected readonly COMPLEX_MESSAGE_TYPE = COMPLEX_MESSAGE_TYPE;

  actionOnClick(action: any) {
    this.onActionClick.emit(action);
  }
}
