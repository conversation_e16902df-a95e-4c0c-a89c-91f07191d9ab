<div class="gallery">
  <div class="main-image">
    <div class="image-label" *ngIf="selectedImage?.label">{{ selectedImage.label }}</div>
    <img [src]="selectedImage?.src" [alt]="selectedImage?.label" class="zoom">
  </div>
  <div *ngIf="imageSrcs.length > 1" class="thumbnail-container-wrapper">
    <button class="scroll-button left" (click)="scrollLeft()">&#10094;</button>
    <div class="thumbnail-container" #thumbnailContainer>
      <div
        class="thumbnail"
        *ngFor="let image of imageSrcs"
        (click)="onSelect(image)"
        [class.selected]="image === selectedImage">
        <img [src]="image.src" [alt]="image.label">
      </div>
    </div>
    <button class="scroll-button right" (click)="scrollRight()">&#10095;</button>
  </div>
</div>
