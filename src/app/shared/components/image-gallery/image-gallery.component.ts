import { Component, Input, OnChanges, SimpleChanges, ViewChild, ElementRef, AfterViewInit } from '@angular/core';

@Component({
  selector: 'app-image-gallery',
  templateUrl: './image-gallery.component.html',
  styleUrls: ['./image-gallery.component.scss']
})
export class ImageGalleryComponent implements OnChanges, AfterViewInit {
  @Input() imageSrcs: any[] = [];

  selectedImage: any;
  selectedImageZoom: any;
  shouldZoom: boolean = false;

  @ViewChild('thumbnailContainer') thumbnailContainer!: ElementRef;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['imageSrcs'] && changes['imageSrcs'].currentValue) {
      this.selectedImage = this.imageSrcs[0]; // Hoặc bạn có thể chọn ảnh mặc định khác nếu cần
      console.log(this.imageSrcs)
      this.scrollToSelectedImage();
    }
  }

  ngAfterViewInit(): void {
    this.scrollToSelectedImage();
  }

  onSelect(image: any): void {
    this.selectedImage = image;
    this.scrollToSelectedImage();
  }

  onImageClick(imgSrc: string) {
    console.log('Image clicked with src:', imgSrc);
    this.selectedImageZoom = imgSrc;
    this.shouldZoom = true;
    setTimeout(() => {
      this.shouldZoom = false; // Reset the trigger after zoom
    }, 100);
  }

  scrollLeft(): void {
    const currentIndex = this.imageSrcs.indexOf(this.selectedImage);
    if (currentIndex > 0) {
      this.selectedImage = this.imageSrcs[currentIndex - 1];
      this.scrollToSelectedImage();
    }
  }

  scrollRight(): void {
    const currentIndex = this.imageSrcs.indexOf(this.selectedImage);
    if (currentIndex < this.imageSrcs.length - 1) {
      this.selectedImage = this.imageSrcs[currentIndex + 1];
      this.scrollToSelectedImage();
    }
  }

  private scrollToSelectedImage(): void {
    if (this.thumbnailContainer) {
      const container = this.thumbnailContainer.nativeElement;
      setTimeout(() => {
        const selectedThumbnail = container.querySelector('.thumbnail.selected') as HTMLElement;

        if (selectedThumbnail){
          selectedThumbnail.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center'
          });
        }
      }, 100)

    }
  }
}
