import {AfterViewInit, Component, Input, OnChanges, SimpleChanges} from '@angular/core';

@Component({
  selector: 'app-image-grid',
  templateUrl: './image-grid.component.html',
  styleUrls: ['./image-grid.component.scss']
})
export class ImageGridComponent  implements OnChanges{
  @Input() imageSrcs: { src: string; label?: string }[] = [];

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['items'] && changes['items'].currentValue) {
    }
  }
}
