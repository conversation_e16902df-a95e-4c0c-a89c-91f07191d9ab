import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {SignInComponent} from "../../components/sign-in/sign-in.component";
import {ForgetPassComponent} from "../../components/forget-pass/forget-pass.component";


const routes: Routes = [
  { path: 'sign-in', component: SignInComponent },
  { path: 'forget-password', component: ForgetPassComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule { }
