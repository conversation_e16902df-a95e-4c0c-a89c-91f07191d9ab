

::ng-deep .mat-mdc-form-field.mat-form-field-appearance-fill {
  .mat-mdc-text-field-wrapper {
    .mat-mdc-form-field-flex {
      height: 56px;
      border-color: #33383F;
      border-radius: 12px;

      &:hover {
        border-color: #ffffff;
      }

      &:focus {
        border-image-source: linear-gradient(357.79deg, #5599FF 2.13%, #7241FF 83.8%, #8E66FF 99.72%);
      }
    }
  }
}

::ng-deep .mdc-form-field {
  .mdc-checkbox {
    .mdc-checkbox__background {
      border-color: white !important;
      border-width: 1px;
      height: 20px;
      width: 20px;
      border-radius: 4px;
    }
  }
}

