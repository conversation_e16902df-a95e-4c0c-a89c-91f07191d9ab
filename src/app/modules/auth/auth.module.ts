import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuthRoutingModule } from './auth-routing.module';
import {SignInComponent} from "../../components/sign-in/sign-in.component";
import {ForgetPassComponent} from "../../components/forget-pass/forget-pass.component";
import {SharedModule} from "@shared/shared.module";


@NgModule({
  declarations: [
    SignInComponent,
    ForgetPassComponent
  ],
  imports: [
    CommonModule,
    AuthRoutingModule,
    SharedModule
  ]
})
export class AuthModule { }
