import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { QueryGptRoutingModule } from './query-gpt-routing.module';
import {WidgetBoardComponent} from "../../components/widget-board/widget-board.component";
import {DiagramComponent} from "../../components/diagram/diagram.component";
import {ChatMessageComponent} from "../../components/chat-message/chat-message.component";
import {SharedModule} from "@shared/shared.module";
import {FormsModule} from "@angular/forms";
import {AddOrEditChartModule} from "../../chart-component/add-or-edit-chart.module";
import {MatTableModule} from "@angular/material/table";
import {MatTabsModule} from "@angular/material/tabs";
import {BrowserAnimationsModule} from "@angular/platform-browser/animations";
import {ClipboardModule} from "@angular/cdk/clipboard";
import {QueryGptComponent} from "./query-gpt.component";
import {CustomNumberPipePipe} from "../../shared/pipes/custom-number-pipe.pipe";
import {ClickOutsideDirective} from "../../shared/directives/click-outside.directive";
import {MatSnackBarModule} from "@angular/material/snack-bar";
import {NgScrollbarModule} from "ngx-scrollbar";
import {AppModule} from "../../app.module";


@NgModule({
  declarations: [
    WidgetBoardComponent,
    DiagramComponent,
    ChatMessageComponent,
    CustomNumberPipePipe,
    ClickOutsideDirective,
  ],
  imports: [
    CommonModule,
    QueryGptRoutingModule,
    FormsModule,
    AddOrEditChartModule,
    MatTableModule,
    MatTabsModule,
    // BrowserAnimationsModule,
    ClipboardModule,
    NgScrollbarModule,
    AppModule,
  ]
})
export class QueryGptModule { }
