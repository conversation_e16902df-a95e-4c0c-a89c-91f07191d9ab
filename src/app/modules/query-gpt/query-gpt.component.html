<div class="bg-[#010314] h-screen flex flex-col max-w-screen ">
  <!--  header-->
  <div class="header w-full px-4 lg:px-8 py-4 flex gap-2 ">
    <img class=" lg:hidden  z-10" [ngClass]="showSidebar ? 'hidden': ''" src="assets/svg/showNav.svg" alt="My SVG"
         (click)="handleShowSidebar(true)">

    <svg width="108" height="28" viewBox="0 0 206 53" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9.9329 23.5411H13.5239V32.718C12.0385 31.8215 10.8034 30.5647 9.9329 29.064V23.5411ZM41.7458 42.5039C41.9087 42.6689 42 42.8915 42 43.1233C42 43.3552 41.9087 43.5778 41.7458 43.7428L38.7428 46.7458C38.5778 46.9087 38.3552 47 38.1233 47C37.8915 47 37.6689 46.9087 37.5039 46.7458L30.0699 39.3119C30.0658 39.3079 30.0617 39.3038 30.0577 39.2996C30.0227 39.2646 30.0087 39.2209 29.9824 39.1841C26.7643 41.5336 22.8826 42.7991 18.8981 42.7979C8.46116 42.7979 0 34.3367 0 23.8981C0 13.4594 8.46116 5 18.8998 5C29.3384 5 37.7979 13.4612 37.7979 23.8998C37.7979 28.0455 36.4469 31.8675 34.1824 34.9824C34.2261 35.0122 34.2734 35.0314 34.3119 35.0699L41.7458 42.5039ZM32.9469 23.8981C32.9424 21.1259 32.1165 18.4172 30.5736 16.1141C29.0307 13.8111 26.8399 12.0168 24.2779 10.958C21.7159 9.89926 18.8976 9.62341 16.1789 10.1653C13.4602 10.7073 10.9631 12.0427 9.00287 14.0029C7.04265 15.9631 5.70726 18.4602 5.16534 21.1789C4.62341 23.8976 4.89926 26.7159 5.95804 29.2779C7.01682 31.8399 8.81105 34.0307 11.1141 35.5736C13.4172 37.1165 16.1259 37.9424 18.8981 37.9469H18.9401C20.7822 37.9441 22.6058 37.5786 24.3067 36.8711C26.0076 36.1636 27.5524 35.128 28.8531 33.8234C30.1538 32.5188 31.1847 30.9709 31.8871 29.2679C32.5896 27.5649 32.9497 25.7402 32.9469 23.8981ZM24.442 32.613C25.9352 31.6604 27.1633 30.3456 28.012 28.791V25.704H24.442V32.613ZM16.5671 34.2719C17.3406 34.3717 18.1193 34.4119 18.8981 34.3979C19.598 34.4417 20.298 34.4417 20.998 34.3979V18.4609H16.5688V34.2719H16.5671Z"
        fill="url(#paint0_linear_3_282)"/>
      <path d="M206 15.7937V19.8055H199.396V40.5746H194.426V19.8055H187.822V15.7937H206Z"
            fill="url(#paint1_linear_3_282)"/>
      <path
        d="M184.897 23.4623C184.897 24.7877 184.578 26.0303 183.939 27.1901C183.323 28.3498 182.341 29.2847 180.992 29.9948C179.667 30.7049 177.986 31.0599 175.951 31.0599H171.797V40.5746H166.826V15.7937H175.951C177.868 15.7937 179.501 16.1251 180.85 16.7878C182.199 17.4505 183.205 18.3617 183.868 19.5215C184.554 20.6812 184.897 21.9948 184.897 23.4623ZM175.738 27.0481C177.11 27.0481 178.128 26.7404 178.791 26.125C179.454 25.486 179.785 24.5984 179.785 23.4623C179.785 21.0481 178.436 19.841 175.738 19.841H171.797V27.0481H175.738Z"
        fill="url(#paint2_linear_3_282)"/>
      <path
        d="M156.119 23.2492C155.551 22.2078 154.77 21.4149 153.776 20.8705C152.782 20.3262 151.622 20.054 150.297 20.054C148.829 20.054 147.528 20.3853 146.392 21.0481C145.256 21.7108 144.368 22.6575 143.729 23.8883C143.09 25.119 142.77 26.5391 142.77 28.1486C142.77 29.8054 143.09 31.2492 143.729 32.4799C144.392 33.7107 145.303 34.6574 146.463 35.3202C147.622 35.9829 148.972 36.3142 150.51 36.3142C152.403 36.3142 153.954 35.8172 155.161 34.8231C156.368 33.8054 157.161 32.3971 157.54 30.5983H149.019V26.7995H162.439V31.1308C162.108 32.8586 161.397 34.4563 160.309 35.9237C159.22 37.3912 157.812 38.5746 156.084 39.474C154.38 40.3497 152.463 40.7876 150.332 40.7876C147.942 40.7876 145.776 40.255 143.835 39.19C141.918 38.1012 140.404 36.5983 139.291 34.6811C138.202 32.764 137.658 30.5865 137.658 28.1486C137.658 25.7108 138.202 23.5332 139.291 21.6161C140.404 19.6753 141.918 18.1723 143.835 17.1072C145.776 16.0185 147.93 15.4741 150.297 15.4741C153.09 15.4741 155.516 16.1605 157.575 17.5333C159.634 18.8824 161.054 20.7877 161.835 23.2492H156.119Z"
        fill="url(#paint3_linear_3_282)"/>
      <path
        d="M134.981 17.7839L121.257 51.3046H117.348L121.839 40.3251L112.648 17.7839H116.849L124.002 36.2494L131.072 17.7839H134.981Z"
        fill="white"/>
      <path
        d="M102.79 21.4852C103.455 20.1821 104.398 19.1701 105.618 18.4492C106.865 17.7284 108.377 17.3679 110.151 17.3679V21.2773H109.153C104.911 21.2773 102.79 23.5785 102.79 28.181V40.5746H99.0051V17.7838H102.79V21.4852Z"
        fill="white"/>
      <path
        d="M93.178 28.3057C93.178 29.0266 93.1364 29.7891 93.0532 30.5931H74.8373C74.9759 32.8389 75.7383 34.5995 77.1246 35.8749C78.5387 37.1226 80.2438 37.7464 82.2401 37.7464C83.8759 37.7464 85.2345 37.3721 86.3158 36.6235C87.4248 35.8472 88.2012 34.8213 88.6448 33.5459H92.7205C92.1105 35.7363 90.8906 37.5246 89.0607 38.9109C87.2308 40.2695 84.9572 40.9488 82.2401 40.9488C80.0775 40.9488 78.1366 40.4636 76.4176 39.4932C74.7263 38.5227 73.3955 37.1503 72.4251 35.3758C71.4547 33.5737 70.9695 31.4942 70.9695 29.1375C70.9695 26.7808 71.4408 24.7152 72.3835 22.9408C73.3262 21.1663 74.6432 19.8077 76.3345 18.865C78.0535 17.8946 80.022 17.4094 82.2401 17.4094C84.4027 17.4094 86.3158 17.8808 87.9794 18.8234C89.6429 19.7661 90.9183 21.0693 91.8055 22.7328C92.7205 24.3686 93.178 26.2263 93.178 28.3057ZM89.2686 27.5155C89.2686 26.0738 88.9498 24.84 88.3121 23.8141C87.6744 22.7605 86.801 21.9703 85.692 21.4436C84.6107 20.889 83.4046 20.6118 82.0737 20.6118C80.1606 20.6118 78.5248 21.2217 77.1662 22.4417C75.8354 23.6616 75.0729 25.3529 74.8788 27.5155H89.2686Z"
        fill="white"/>
      <path
        d="M65.1614 17.7839V40.5747H61.3768V37.206C60.6559 38.3705 59.6439 39.2854 58.3408 39.9508C57.0654 40.5885 55.6514 40.9074 54.0987 40.9074C52.3243 40.9074 50.73 40.547 49.316 39.8261C47.902 39.0775 46.7791 37.9684 45.9473 36.499C45.1432 35.0295 44.7412 33.2412 44.7412 31.134V17.7839H48.4842V30.6349C48.4842 32.8807 49.0526 34.6136 50.1894 35.8335C51.3261 37.0258 52.8788 37.6219 54.8473 37.6219C56.8713 37.6219 58.4656 36.998 59.6301 35.7504C60.7945 34.5027 61.3768 32.6866 61.3768 30.3022V17.7839H65.1614Z"
        fill="white"/>
      <defs>
        <linearGradient id="paint0_linear_3_282" x1="21.2079" y1="46.876" x2="19.5639" y2="4.36404"
                        gradientUnits="userSpaceOnUse">
          <stop stop-color="#5599FF"/>
          <stop offset="0.836794" stop-color="#7241FF"/>
          <stop offset="1" stop-color="#8E66FF"/>
        </linearGradient>
        <linearGradient id="paint1_linear_3_282" x1="152.603" y1="10.8061" x2="212.429" y2="30.8362"
                        gradientUnits="userSpaceOnUse">
          <stop offset="0.0695937" stop-color="#4467FF"/>
          <stop offset="1" stop-color="#7241FF"/>
        </linearGradient>
        <linearGradient id="paint2_linear_3_282" x1="152.604" y1="10.8061" x2="212.429" y2="30.8362"
                        gradientUnits="userSpaceOnUse">
          <stop offset="0.0695937" stop-color="#4467FF"/>
          <stop offset="1" stop-color="#7241FF"/>
        </linearGradient>
        <linearGradient id="paint3_linear_3_282" x1="152.604" y1="10.806" x2="212.429" y2="30.8362"
                        gradientUnits="userSpaceOnUse">
          <stop offset="0.0695937" stop-color="#4467FF"/>
          <stop offset="1" stop-color="#7241FF"/>
        </linearGradient>
      </defs>
    </svg>
  </div>

  <!--  content-->
  <div class="flex-1 lg:flex lg:flex-row relative">
    <div class="sidebar fixed top-0 lg:static z-10  lg:w-1/6 p-8 text-white h-full flex flex-col justify-between"
         [ngClass]="{'w-5/6 active': showSidebar }">
      <img src="assets/svg/close.svg" class="absolute top-4 right-0 bg-white translate-x-full p-2 lg:hidden border"
           (click)="handleShowSidebar(false)" [ngClass]="{'hidden': !showSidebar }">
      <div>
        <!--      <div class="grid justify-items-center rounded-full py-3 mb-4 bg-[#7241FF]">-->
        <!--        <div class="flex gap-2">-->
        <!--          <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">-->
        <!--            <path d="M20 13H13.5V19.5H11.5V13H5V11H11.5V4.5H13.5V11H20V13Z" fill="white"/>-->
        <!--          </svg>-->
        <!--          <p>Start new chat</p>-->
        <!--        </div>-->
        <!--      </div>-->
        <!--      list-->
        <div class="flex flex-col gap-2">
          <div class="flex p-2 justify-between rounded cursor-pointer text-[#94A2B8]" routerLink="/query-gpt/chat"
               (click)="showSidebar = !showSidebar" routerLinkActive="menu-active"
               [routerLinkActiveOptions]="{ exact: true }">
            <div class="flex gap-2">
              <svg width="24" height="24" viewBox="0 0 24 24"
                   fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M19.9641 7.98755H9.57659C8.59221 7.98755 7.79065 8.78911 7.79065 9.77349V17.0063C7.79065 17.9907 8.59221 18.7875 9.57659 18.7875H15.5203C15.6516 18.7875 15.7828 18.8438 15.8766 18.9375L18.8297 21.661C18.9938 21.8204 19.2656 21.7547 19.2656 21.525V19.1532C19.2656 18.8719 19.4438 18.7829 19.725 18.7829H19.7719C20.7563 18.7829 21.7453 17.9907 21.7453 17.0016V9.77349C21.75 8.78442 20.9485 7.98755 19.9641 7.98755Z"/>
                <path
                  d="M8.175 6.83906H16.4906V3.77812C16.4906 2.93437 15.8063 2.25 14.9625 2.25H3.77812C2.93437 2.25 2.25 2.93437 2.25 3.77812V11.5219C2.25 12.3656 2.93437 13.05 3.77812 13.05H6.64219V8.36719C6.64687 7.52344 7.33125 6.83906 8.175 6.83906Z"/>
              </svg>
              <p>AI conversation</p>
            </div>
            <div>
              <svg (click)="openClearConversationDia(); $event.stopPropagation();" class="transform rotate-180" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_32147_2674)">
                  <path d="M9.5 19.5V18H4.5C3.95 18 3.45 17.78 3.09 17.41C2.72 17.05 2.5 16.55 2.5 16C2.5 14.97 3.3 14.11 4.31 14.01C4.37 14 4.43 14 4.5 14H19.5C19.57 14 19.63 14 19.69 14.01C20.17 14.05 20.59 14.26 20.91 14.59C21.32 14.99 21.54 15.56 21.49 16.18C21.4 17.23 20.45 18 19.39 18H14.5V19.5C14.5 20.88 13.38 22 12 22C10.62 22 9.5 20.88 9.5 19.5Z" stroke="#4C5153" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M20.1699 5.3L19.6899 14.01C19.6299 14 19.5699 14 19.4999 14H4.49992C4.42992 14 4.36992 14 4.30992 14.01L3.82992 5.3C3.64992 3.53 5.03992 2 6.80992 2H17.1899C18.9599 2 20.3499 3.53 20.1699 5.3Z" stroke="#4C5153" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M7.98999 2V7" stroke="#4C5153" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 2V4" stroke="#4C5153" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </g>
                <defs>
                  <clipPath id="clip0_32147_2674">
                    <rect width="24" height="24" fill="white"/>
                  </clipPath>
                </defs>
              </svg>
            </div>
          </div>
          <div class="flex gap-2 p-2 rounded cursor-pointer text-[#94A2B8]" routerLink="/query-gpt/diagram"
               (click)="showSidebar = !showSidebar" routerLinkActive="menu-active"
               [routerLinkActiveOptions]="{ exact: true }">
            <svg width="24" height="24" viewBox="0 0 24 24"
                 fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M19.125 3H4.875C3.83948 3 3 3.83944 3 4.875V19.125C3 20.1606 3.83948 21 4.875 21H19.125C20.1606 21 21 20.1606 21 19.125V4.875C21 3.83944 20.1606 3 19.125 3ZM14.25 17.25H6.75V15H14.25V17.25ZM17.25 13.125H6.75V10.875H17.25V13.125ZM17.25 9H6.75V6.75H17.25V9Z"/>
            </svg>
            <p>Data Schema</p>
          </div>
          <div *ngIf="position === 'CEO'" class="flex gap-2 p-2 rounded cursor-pointer text-[#94A2B8]" routerLink="/query-gpt/list-feedback"
               (click)="showSidebar = !showSidebar" routerLinkActive="menu-active"
               [routerLinkActiveOptions]="{ exact: true }">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6 4C5.44772 4 5 4.44772 5 5V19C5 19.5523 5.44772 20 6 20H18C18.5523 20 19 19.5523 19 19V12C19 11.4477 19.4477 11 20 11C20.5523 11 21 11.4477 21 12V19C21 20.6569 19.6569 22 18 22H6C4.34315 22 3 20.6569 3 19V5C3 3.34315 4.34315 2 6 2H12C12.5523 2 13 2.44772 13 3C13 3.55228 12.5523 4 12 4H6Z"
                    fill="#6F767E"/>
              <path
                d="M19.4958 3.76776C18.5195 2.79144 16.9366 2.79144 15.9602 3.76776L15.2531 4.47486C15.0579 4.67012 15.0579 4.98671 15.2531 5.18197L18.7887 8.7175C18.9839 8.91277 19.3005 8.91277 19.4958 8.7175L20.2029 8.0104C21.1792 7.03409 21.1792 5.45117 20.2029 4.47486L19.4958 3.76776Z"
                fill="#6F767E"/>
              <path
                d="M13.8389 6.59619C13.6436 6.40093 13.327 6.40093 13.1318 6.59619L8.06419 11.6638C7.91119 11.8168 7.81194 12.0153 7.78134 12.2295L7.30994 15.5293C7.21566 16.1893 7.78134 16.755 8.44131 16.6607L11.7411 16.1893C11.9553 16.1587 12.1538 16.0594 12.3068 15.9064L17.3744 10.8388C17.5697 10.6436 17.5697 10.327 17.3744 10.1317L13.8389 6.59619Z"
                fill="#6F767E"/>
            </svg>
            <p>List feedback</p>
          </div>
        </div>
      </div>
      <div class=" w-full rounded-2xl">
        <div *ngIf="showDiv" (clickOutside)="showDiv = false">
          <!--          <div *ngFor="let role of roleSignIn"-->
          <!--               class="p-2 cursor-pointer"-->
          <!--               routerLink="/sign-in"-->
          <!--               [ngClass]="roleActive === role.name ? 'bg-[#0A0C22] text-[#7241FF]' :''"-->
          <!--               (click)="changeRole(role.name)">-->
          <!--            <p>{{role.title}}</p>-->
          <!--          </div>-->
          <div
            class="p-2 cursor-pointer"
            routerLink="/auth/sign-in"
            (click)="signOut()">
            <p>Sign Out</p>
          </div>
        </div>
        <div class="flex justify-between mt-4">
          <div class="flex gap-2">
            <div class="rounded-full h-8 w-8 border-2 border-[#7241FF] bg-[#CABDFF] text-sm flex items-center justify-center text-[#7241FF]">
              {{ nameAcc.charAt(0).toUpperCase() }}
            </div>
            <p class="flex items-center">{{nameAcc}}</p>
          </div>
          <svg (click)="toggleDiv()" class="cursor-pointer" width="24" height="24" viewBox="0 0 24 24" fill="none"
               xmlns="http://www.w3.org/2000/svg">
            <path
              d="M13.875 6.375C13.875 5.34366 13.0313 4.5 12 4.5C10.9687 4.5 10.125 5.34366 10.125 6.375C10.125 7.40634 10.9687 8.25 12 8.25C13.0313 8.25 13.875 7.40634 13.875 6.375ZM13.875 17.625C13.875 16.5937 13.0313 15.75 12 15.75C10.9687 15.75 10.125 16.5937 10.125 17.625C10.125 18.6563 10.9687 19.5 12 19.5C13.0313 19.5 13.875 18.6563 13.875 17.625ZM13.875 12C13.875 10.9687 13.0313 10.125 12 10.125C10.9687 10.125 10.125 10.9687 10.125 12C10.125 13.0313 10.9687 13.875 12 13.875C13.0313 13.875 13.875 13.0313 13.875 12Z"
              fill="#E6E6E6"/>
          </svg>
        </div>
      </div>
    </div>
    <div class="w-full lg:w-5/6 h-full z-0">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>

<ng-template #confirm>
  <div class="p-6">
    <div class="header border-b flex justify-between items-center pb-3">
      <div class="text-xl font-bold card-title text-white">
        Clear conversation
      </div>
      <div class="rounded-full flex hover:bg-slate-200 cursor-pointer">
        <svg (click)="dialogService.closeAll()" width="24" height="24" viewBox="0 0 24 24" fill="none"
             xmlns="http://www.w3.org/2000/svg">
          <path
            d="M6.70708 5.29289C6.31655 4.90237 5.68339 4.90237 5.29286 5.29289C4.90234 5.68342 4.90234 6.31658 5.29286 6.70711L10.5857 12L5.29277 17.2929C4.90225 17.6834 4.90225 18.3166 5.29277 18.7071C5.6833 19.0976 6.31646 19.0976 6.70698 18.7071L11.9999 13.4142L17.2929 18.7071C17.6834 19.0976 18.3166 19.0976 18.7071 18.7071C19.0976 18.3166 19.0976 17.6834 18.7071 17.2929L13.4141 12L18.707 6.70711C19.0975 6.31658 19.0975 5.68342 18.707 5.29289C18.3165 4.90237 17.6833 4.90237 17.2928 5.29289L11.9999 10.5857L6.70708 5.29289Z"
            fill="#6F767E"/>
        </svg>
      </div>
    </div>
    <!--    content-->
    <div class="content mt-6  text-white">
      <div>Are you want to clear conversation ?</div>
    </div>
    <!--    footer-->
    <div class="footer mt-6 flex justify-end">
      <button mat-raised-button class="mr-3" (click)="dialogService.closeAll()"
      >
        <span>cancel</span>
      </button>
      <button mat-raised-button color="primary" class="mr-3" (click)="clearConversation();dialogService.closeAll()">
        <span>yes</span>
      </button>
    </div>
  </div>
</ng-template>
