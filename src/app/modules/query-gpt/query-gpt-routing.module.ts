import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {AuthComponent} from "../auth/auth.component";
import {QueryGptComponent} from "./query-gpt.component";
import {ChatMessageComponent} from "../../components/chat-message/chat-message.component";
import {DiagramComponent} from "../../components/diagram/diagram.component";
import {ShowFeedbackComponent} from "../../components/show-feedback/show-feedback.component";

const routes: Routes = [
  { path: 'chat', component: ChatMessageComponent },
  { path: 'diagram', component: DiagramComponent },
  { path: 'list-feedback', component: ShowFeedbackComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class QueryGptRoutingModule { }
