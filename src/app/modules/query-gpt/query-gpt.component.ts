import {Component, ElementRef, Injector, OnInit, Renderer2, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {LogInService} from "../../shared/services/log-in.service";
import {environment} from "../../../environments/environment";
import {BaseComponent} from "@core/base.component";
import {ChatConversationService} from "../../shared/services/chat-conversation.service";

@Component({
  selector: 'app-query-gpt',
  templateUrl: './query-gpt.component.html',
  styleUrls: ['./query-gpt.component.scss']
})
export class QueryGptComponent extends BaseComponent implements OnInit {
  @ViewChild('confirm') confirm: any;

  idActiveLink: number = 0;
  showDiv: any;
  showSidebar = false

  nameAcc: string

  roleSignIn = [{
    title: "Sign in as CEO",
    name: "CEO"
  }, {
    title: "Sign in as BU1 manager",
    name: "BU1"
  }, {
    title: "Sign in as staff",
    name: "Staff"
  }];
  position = '';

  constructor(private route: ActivatedRoute,
              private router: Router,
              private renderer: Renderer2,
              private el: ElementRef,
              private loginService: LogInService,
              private conversationService: ChatConversationService,
              injector: Injector) {
    super(injector)
  }

  ngOnInit() {
    this.loginService.account$.subscribe((user: any) => {
      this.position = user.position;
      this.nameAcc = user.fullName;
    })
  }

  toggleDiv() {
    this.showDiv = !this.showDiv;
    console.log(this.nameAcc)
  }

  handleShowSidebar(status) {
    this.showSidebar = status
  }

  signOut(): void {
    localStorage.clear();
    sessionStorage.clear();
  }

  openClearConversationDia() {
    this.showDialog(this.confirm, {
      data: {},
      width: '30vw',
    },)
  }

  clearConversation() {
    const newConversation = [];
    this.conversationService.clearConversation(newConversation);
    localStorage.removeItem('chatmess')
  }
}
