.mat-mdc-form-field.mat-form-field-appearance-fill {
  .mat-mdc-text-field-wrapper {
    //margin-bottom: 16px;
    padding: 0;
    padding-bottom: 0;
    background-color: transparent;
    width: 100%;

    .mat-mdc-form-field-flex {
      position: relative;
      display: flex;
      align-items: stretch;
      height: 40px;
      border-radius: 6px;
      padding: 0 16px;
      border-width: 1px;
      background-color: transparent;

      .mat-mdc-form-field-infix{
        display: flex;
        align-items: center;
        width: 88px;
        padding: 0;
        border: 0;
        min-height: 40px;

        .mdc-floating-label{
        }

        .mat-mdc-input-element:-webkit-autofill {
          background-color: transparent !important;
          background-image:none !important;
        }

        .mat-mdc-select{
          display: flex;
          align-items: center;

          .mat-mdc-select-trigger{
            display: flex;
            align-items: center;

            .mat-mdc-select-value{
              display: flex;
              max-width: none;
            }

            .mat-mdc-select-arrow-wrapper {
              display: flex;
              align-items: center;
              transform: none;
              margin-left: 4px;

              .mat-mdc-select-arrow {
                min-height: 0;
              }
            }
          }
        }
      }
    }


    /* Remove the underline */
    .mdc-line-ripple::after,
    .mdc-line-ripple::before {
      display: none;
    }
  }
}
// doi mau cua check box
.mdc-form-field {
  .mdc-checkbox {
    .mdc-checkbox__background {
      .mdc-checkbox__checkmark{
        background-color: #7241FF;
        border-radius: 4px;
      }
    }
  }
}

.cdk-overlay-dark-backdrop{
  background-color: rgba(64, 64, 66, 0.7) !important;
}

.mat-mdc-dialog-surface {
  border-radius: 24px !important;
  background-color: #010314 !important;
}

.mat-mdc-option {
  .mat-pseudo-checkbox{
    display: none;
  }
}

.mdc-snackbar{
  border-radius: 4px;
  .mdc-snackbar__surface{
    background-color: transparent !important;
  }
}




