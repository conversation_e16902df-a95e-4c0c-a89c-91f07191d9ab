const OpenAI = require("openai");
const dbPool = require('./database/database');
require('dotenv').config();
const tiktoken = require('tiktoken');
const openai = new OpenAI();
const fs = require("fs");
// const { parse } = require("csv-parse");
const cosineDistance = require('compute-cosine-distance');

async function ask(model, messages, maxRetries = 3) {
  let retryCount = 0;

  while (retryCount < maxRetries) {
    try {
      const tokenCount = await countTokensInMessages(messages);
      console.log('Calling chatGPT, total token: ', tokenCount);
      console.log('Messages: ', messages, JSON.stringify(messages));
      console.log('Model: ', model);

      // Search similar questions and answers
      const examples = await searcSimilarText(messages[messages.length - 1].content);
      examples[0]['tables'].forEach(item => {
        messages[0].content += `${item}\n\n`
      });
      messages[0].content += '\nExamples:\n';

      examples.forEach(item => {
        messages[0].content += `Q: ${item['questions']}\nA: ${item['answers']}\n\n---\n\n`;
      });
      console.log(messages);

      const completion = await openai.chat.completions.create({
        messages,
        model,
        temperature: 0,
        seed: 42,
      });

      console.log(completion.choices[0]);
      return completion.choices[0].message;
    } catch (error) {
      console.error('Error occurred:', error.message);
      retryCount++;

      if (retryCount < maxRetries) {
        console.log(`Retrying (attempt ${retryCount})...`);
        // Add a delay before retrying (e.g., using setTimeout)
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        console.error('Max retries reached. Giving up.');
        // throw error; // If you want to propagate the error after retries are exhausted
      }
    }
  }
}

async function searcSimilarText(text, model = 'text-embedding-ada-002', numItems = 1) {
  let df = [];
  fs.readFile("sys_prompt/embedded_questions.jsonl", 'utf8', function (err, data) {
    if (err) throw err;
    df = JSON.parse(data); //.replaceAll('\\r', '\\')
  });
  const distances = [];
  const embedding = await openai.embeddings.create({
    input: text,
    model: model,
  }).then((res) => res.data[0].embedding);

  // Compute distance between current question embedding with all saved question embeddings
  df.forEach((row) => {
    const distance = cosineDistance(row.ada_embedding, embedding);
    distances.push({distance, ...row});
  });
  distances.sort((a, b) => a.distance - b.distance);  // Sort by distance from 0.0 to 1.0
  return distances.slice(0, numItems);
}

async function queryDB(sql) {
  let connection;
  try {
    console.log('sql: ', sql);
    connection = await dbPool.getConnection();
    return await connection.query(sql);
  } catch (error) {
    console.log('Error: ', error);
    return null;
  } finally {
    if (connection) await connection.release(); // release the connection back to the pool
  }
}

async function formAnswer(model, data) {
  try {

    const contextMsg = {
      role: 'system', content: `Answer the question based on the context below:

    NOTE:
    - Identify the language of the question first.
    - Numeric value: using the following suffix k, M, B to represent the data (1000 -> 1k, 1000000 -> 1M, 1000000000 -> 1B).
    - Context contains token <INF> -> Answer by repeating the question with 'as follows:' (E.g. Question: Doanh thu theo tháng? -> Answer: "Doanh thu theo tháng chi tiết như sau:")
    - Write the answer in json format:
    {
"language" : <language of the question>,
        "answer": <based on the language of the question>,
        "chartName": <based on the language of the question>
    }

Example:
    ---
    Question: Doanh thu công ty tháng 2?
    Context: [{month: null, revenue: null}]
    Answer:
    {
        "answer": "Không có dữ liệu",
        "chartName": null
    }
---
    Question: Doanh thu, chi phí, lợi nhuận công ty tháng này?
    Context: [{month: 11/2023, revenue: 1154000000, cost: 930000000, profit: 224000000}]
    Answer:
    {
        "answer": "Doanh thu công ty tháng 11/2023 đạt 1.15B, chi phí đạt 930M, lợi nhuận đạt 224M",
        "chartName":"Biểu đồ doanh thu, chi phí, lợi nhuận công ty"
    }
    ---
    Question: Tổng quan tài chính của công ty năm nay
    Context: <INF>
    Answer:
    {
        "answer": "Tổng quan tài chính của công ty năm nay chi tiết như sau:",
        "chartName": "Biểu đồ tổng quan tài chính"
    }
---`,
    };
    const context = data.data.length * Object.keys(data.data[0]).length > 6 ? "[<INF>]" : data.data;
    console.log('Data: ', data, typeof context);
    context.forEach((item) => {
      const keys = Object.keys(item);
      keys.forEach((k) => {
        if (typeof item[k] === 'bigint') {
          item[k] = item[k].toString();
        }
      })
    })
    const question = {
      role: 'user',
      content: `Question: ${data.question}\nContext: ${JSON.stringify(context)}\nAnswer: \`\`\`json\n`
    };
    const messages = [contextMsg, question];
    console.log(messages);
    const completion = await openai.chat.completions.create({
      messages,
      model: model,
      temperature: 0,
    });

    console.log(completion.choices[0]);
    const res = completion.choices[0].message.content.split("```json");
    return res[res.length - 1].replace("```", "");
  } catch (e) {
    console.log('Error formAnswer: ', e);
    return null;
  }
}

async function countTokensInMessages(messages) {
  let totalTokens = 0;
  const encoding = tiktoken.encoding_for_model('gpt-3.5-turbo');
  for (const message of messages) {
    if (message.content) {
      const tokens = encoding.encode(message.content).length;
      totalTokens += tokens;
    }
  }

  return totalTokens;
}

module.exports = {ask, queryDB, formAnswer};
