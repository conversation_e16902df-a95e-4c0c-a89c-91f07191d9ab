# Use an official Node.js runtime as a parent image
FROM node:16.14.2
LABEL authors="binh.phamnguyen"

# Create a directory to hold the application code inside the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to install dependencies
COPY package*.json ./

# Install Node.js dependencies
RUN npm install

# Copy the rest of the application code into the container
COPY . .

# Expose the port that your Express app is listening on
EXPOSE 3000

# Define the command to start your Express.js application
CMD ["node", "index.js"]
