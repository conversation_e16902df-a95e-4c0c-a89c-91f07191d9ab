const mariadb = require('mariadb');
require('dotenv').config();

// Read environment variables or use default values
const pool = mariadb.createPool({
  host: process.env.DB_HOST || 'localhost', // DB_HOST is the environment variable
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'querygpt', // DB_USER is the environment variable
  password: process.env.DB_PASSWORD || '12345678', // DB_PASSWORD is the environment variable
  connectionLimit: process.env.DB_CONN_LIMIT ? parseInt(process.env.DB_CONN_LIMIT, 10) : 5,
  database: process.env.DB_NAME || 'querygpt' // DB_NAME is the environment variable
});

async function getConnection() {
  try {
    return await pool.getConnection();
  } catch (error) {
    console.error('Error getting connection from pool', error);
    throw error;
  }
}

module.exports = {
  getConnection,
  // If you want to expose the pool directly (optional)
  pool
};
