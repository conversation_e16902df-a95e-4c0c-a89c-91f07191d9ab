const bcrypt = require('bcrypt');
const crypto = require('crypto'); // For generating the activation token
const User = require('../models/User');
const jwt = require('jsonwebtoken');
const {Op} = require('sequelize');
const emailService = require('../services/emailService');
const path = require('path');
const ejs = require('ejs');
require('dotenv').config();

const userController = {
  registerUser: async (req, res) => {
    try {
      const {login, fullName, position, password, email} = req.body;
      // Check if the user already exists in the database
      const existingUser = await User.findOne({where: {login}});
      if (existingUser) {
        return res.status(400).json({success: false, message: 'User already exists'});
      }
      // Hash the password using bcrypt
      const hashedPassword = await bcrypt.hash(password, 10); // 10 is the number of salt rounds

      // Generate an activation token using crypto
      const activationToken = crypto.randomBytes(20).toString('hex');

      // Create a new user with encrypted password
      const newUser = await User.create({
        login,
        password: hashedPassword, // Store the hashed password in the database
        email,
        fullName,
        position,
        activationToken
      });

      // Send activation email
      await sendActivationEmail(email, activationToken);

      res.status(201).json({success: true, message: 'User registered successfully', user: newUser});
    } catch (e) {
      console.log('Error creating new user: ', e.message);
      res.status(500).json({success: false, error: 'Registration failed', detailed: e.message});
    }
  },

  loginUser: async (req, res) => {
    try {
      const {login, password} = req.body;

      // Find the user by login (username or email)
      const user = await User.findOne({
        where: {
          [Op.or]: [{login}]
        }
      });

      // If the user is not found
      if (!user) {
        return res.status(401).json({success: false, message: 'Invalid login credentials'});
      }

      // Check if the password matches using bcrypt
      const passwordMatch = await bcrypt.compare(password, user.password);

      // If passwords don't match
      if (!passwordMatch) {
        return res.status(401).json({success: false, message: 'Invalid login credentials'});
      }

      // Passwords match - generate JWT token for authentication
      const token = jwt.sign({userId: user.id, login: user.login}, process.env.SECRET_KEY, {expiresIn: '24h'});

      // Send the token as a response
      res.status(200).json({
        success: true,
        user: {login: user.login, fullName: user.fullName, position: user.position},
        token
      });
    } catch (error) {
      console.error('Error occurred during login:', error);
      res.status(500).json({success: false, error: 'Login failed'});
    }
  },

  activateAccount: async (req, res) => {
    try {
      const {activationToken} = req.params; // Assuming activation token is sent as a parameter
      console.log('Receive account activation request with token: ', activationToken);

      // Find the user based on the activation token
      const user = await User.findOne({where: {activationToken}});

      // If user is not found
      if (!user) {
        return res.status(404).json({success: false, message: 'Invalid activation token'});
      }

      // Activate the user account by updating the 'isActivated' field to true
      user.isActivated = true;
      await user.save();

      // Respond with a success message
      // res.status(200).json({ success: true, message: 'Account activated successfully' });
      res.redirect(`${process.env.FE_URL}`);
    } catch (error) {
      console.error('Error occurred during account activation:', error);
      res.status(500).json({success: false, error: 'Account activation failed'});
    }
  },

  getAccount: async (req, res) => {
    try {
      console.log(req.user);
      const user = await User.findOne({
        where: {
          login: req.user.login
        }
      });

      if (!user) {
        // User not found
        return res.status(404).json({success: false, error: 'User not found'});
      }

      const {fullName, position, login} = user;
      // User found
      return res.status(200).json({success: true, user: {fullName, position, login}});
    } catch (error) {
      console.error('Error occurred during account activation:', error);
      res.status(500).json({success: false, error: 'Account activation failed'});
    }
  }
};

const sendActivationEmail = async (userEmail, activationToken) => {
  const activationLink = `${process.env.BASE_URL}:${process.env.PORT}/api/user/activate/${activationToken}`;
  const subject = 'Activate Your Account';
  // Render the email template with dynamic content
  const emailTemplate = path.join(__dirname, '../email_templates', 'activationEmail.ejs');
  console.log(emailTemplate);
  const renderedTemplate = await ejs.renderFile(emailTemplate, {activationLink});

  try {
    await emailService.sendEmail(userEmail, subject, renderedTemplate);
    console.log('Activation email sent successfully');
  } catch (error) {
    console.error('Error sending activation email:', error);
    throw new Error('Failed to send activation email');
  }
};


module.exports = userController;
