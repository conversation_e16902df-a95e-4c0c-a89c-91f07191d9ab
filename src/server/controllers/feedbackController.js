const Feedback = require('../models/Feedback');

const createFeedback = async (req, res) => {
  try {
    // Assuming you receive the necessary data in the request body
    const { conversation_id, question, response, rating, text } = req.body;

    // Create a new feedback instance
    const newFeedback = await Feedback.create({
      conversation_id,
      question,
      response,
      rating,
      text
    });

    // Respond with the newly created feedback data
    res.status(201).json({ success: true, message: 'Feedback created', feedback: newFeedback });
  } catch (error) {
    console.error('Error creating feedback:', error);
    res.status(500).json({ success: false, error: 'Feedback creation failed' });
  }
};

const getFilteredFeedback = async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;

  // Define filter conditions based on query parameters (modify as needed)
  const filters = {};
  if (req.query.rating) {
    filters.rating = req.query.rating; // Filter by rating (if provided)
  }
  if (req.query.status) {
    filters.status = req.query.status; // Filter by status (if provided)
  }
  // Add more filters as needed based on your model fields

  try {
    const offset = (page - 1) * limit;

    // Fetch feedback entries based on filters and pagination
    const feedback = await Feedback.findAll({
      where: filters,
      offset,
      limit
    });

    // Get total count of feedback entries that match the filters
    const totalCount = await Feedback.count({ where: filters });

    const totalPages = Math.ceil(totalCount / limit);

    res.status(200).json({
      success: true,
      page,
      limit,
      totalPages,
      totalCount,
      feedback
    });
  } catch (error) {
    console.error('Error fetching filtered feedback:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch filtered feedback' });
  }
};

const markFeedbackResolved = async (req, res) => {
  const { feedbackId } = req.params; // Assuming you receive the feedback ID from the request parameters

  try {
    // Find the feedback by ID
    const feedback = await Feedback.findByPk(feedbackId);

    if (!feedback) {
      return res.status(404).json({ success: false, message: 'Feedback not found' });
    }

    // Update the status from 'NEW' to 'RESOLVED'
    feedback.status = 'RESOLVED';

    // Save the updated feedback
    await feedback.save();

    res.status(200).json({ success: true, message: 'Feedback status updated to RESOLVED', feedback });
  } catch (error) {
    console.error('Error updating feedback status:', error);
    res.status(500).json({ success: false, error: 'Failed to update feedback status' });
  }
};


module.exports = { createFeedback, getFilteredFeedback, markFeedbackResolved };
