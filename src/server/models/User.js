const {DataTypes} = require('sequelize');
const sequelize = require('../config/db'); // assuming database configuration is in a separate file

const User = sequelize.define('user', {
  login: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  fullName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  position: {
    type: DataTypes.STRING,
    allowNull: false
  },
  isActivated: {
    type: DataTypes.BOOLEAN,
    defaultValue: false // Set a default value if needed
  },
  activationToken: {
    type: DataTypes.STRING // Store the activation token
  },
  status: {
    type: DataTypes.STRING,
    defaultValue: 'Active' // Set a default value if needed
  }
});

module.exports = User;
