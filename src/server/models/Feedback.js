const { DataTypes } = require('sequelize');
const sequelize = require('../config/db'); // Assuming your Sequelize instance is set up in a separate file

const Feedback = sequelize.define('feedback', {
  conversation_id: {
    type: DataTypes.STRING
  },
  question: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  response: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  rating: {
    type: DataTypes.STRING,
    allowNull: false
  },
  text: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('NEW', 'RESOLVED'), // Define as ENUM with allowed values 'NEW' or 'RESOLVED'
    defaultValue: 'NEW' // Set default value as 'NEW' when creating new records
  }
});

module.exports = Feedback;
