Given an input question, use MySQL syntax to generate a sql query by choosing one or multiple of the following tables.  

NOTE: 
1. If the database includes revelant information to the question, write the query, otherwise write \'NOT FOUND\' and return your answer immediately; 
2. If the question is a yes/no question, write the query to display the details (E.g. the percentage of sth) needed instead of NULL/NOT NULL to answer the question; 
3. If the question is about summarizing all statistical info of sth, write the query with the sum, avg, min, max as extra info.  

### Database content ###  
CREATE TABLE `revenue_cost_static` (   
    `id` bigint(20) NOT NULL AUTO_INCREMENT,   
    `month` varchar(100) NOT NULL,   
    `entity_id` bigint(20) NOT NULL,   
    `entity` varchar(255) NOT NULL,   
    `type` varchar(255) NOT NULL,   
    `value_type` varchar(100) NOT NULL,   
    `value` double NOT NULL,   
    PRIMARY KEY (`id`) 
) 
/* Columns in revenue_cost_static and all categories for low cardinality columns :  
month: 01/2023, 02/2023 
entity_id: 1,2,3 
entity: \'BU 01\', \'BU 02\', \'BU 03\' 
type: DEPARTMENT, PROJECT, CUSTOMER 
value_type: REVENUE, COST, PROFIT, MM 
value: 12000000, 54000000, 324023132 
*/

CREATE TABLE `revenue_cost_plan` (   
    `id` bigint(11) NOT NULL AUTO_INCREMENT,   
    `month` varchar(100) NOT NULL,   
    `entity_id` bigint(20) NOT NULL,   
    `entity` varchar(255) NOT NULL,   
    `type` varchar(100) NOT NULL,   
    `value_type` varchar(255) NOT NULL,   
    `value` double NOT NULL,   
    PRIMARY KEY (`id`) 
)
/* Columns in revenue_cost_plan and all categories for low cardinality columns :  
month: 01/2023, 02/2023 
entity_id: 1,2,3 
entity: \'BU 01\', \'BU 02\', \'BU 03\' 
type: DEPARTMENT, PROJECT, CUSTOMER 
value_type: REVENUE_PLAN, COST_PLAN, PROFIT_PLAN, MM_PLAN 
value: 12000000, 54000000, 324023132 
*/