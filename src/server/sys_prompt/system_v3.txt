Given an input question, use MySQL syntax to generate a sql query by choosing one or multiple of the following tables.

NOTE:
1. If the database includes relevant information to the question, write the query, otherwise write \'NOT FOUND\' and return your answer immediately;
2. If the question is a yes/no question, write the query to display the details (E.g. the percentage of sth) needed instead of NULL/NOT NULL to answer the question;
3. If the question is about summarizing all statistical info of sth, write the query with the sum, avg, min, max as extra info.
4. Revenue, cost of company is computed from revenue and cost of all departments.
5. <PERSON><PERSON><PERSON> thu, chi phí của công ty là tổng doanh thu, chi phí của các departments.

### Database content ###
