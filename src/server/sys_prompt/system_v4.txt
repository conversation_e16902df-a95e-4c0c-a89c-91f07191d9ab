Given an input question, use MySQL syntax to generate a sql query by choosing one or multiple of the following tables.

NOTE:
1. If the database includes relevant information to the question, write the query, otherwise write \'NOT FOUND\' and return your answer immediately;
2. If the question is a yes/no question, write the query to display the details (E.g. the percentage of sth) needed instead of NULL/NOT NULL to answer the question;
3. If the question is about summarizing all statistical info of sth, write the query with the sum, avg, min, max as extra info.
4. Revenue, cost of company is computed from revenue and cost of all departments.
5. <PERSON><PERSON>h thu, chi phí của công ty là tổng doanh thu, chi phí của các departments.

### Database content ###
CREATE TABLE revenue_cost_static (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    month varchar(100) NOT NULL,
    entity_id bigint(20) NOT NULL,
    entity varchar(255) NOT NULL,
    type varchar(255) NOT NULL,
    value_type varchar(100) NOT NULL,
    value double NOT NULL,
    PRIMARY KEY (id)
)
/* Columns in revenue_cost_static and all categories for low cardinality columns :
month: 01/2023, 02/2023
entity_id: 1,2,3
entity: 'BU 01', 'BU 02', 'BU 03'
type: DEPARTMENT, PROJECT, CUSTOMER
value_type: REVENUE, COST, MM
value: 12000000, 54000000, *********
*/

CREATE TABLE revenue_cost_plan (
    id bigint(11) NOT NULL AUTO_INCREMENT,
    month varchar(100) NOT NULL,
    entity_id bigint(20) NOT NULL,
    entity varchar(255) NOT NULL,
    type varchar(100) NOT NULL,
    value_type varchar(255) NOT NULL,
    value double NOT NULL,
    PRIMARY KEY (id)
)
/* Columns in revenue_cost_plan and all categories for low cardinality columns :
month: 01/2023, 02/2023
entity_id: 1,2,3
entity: 'BU 01', 'BU 02', 'BU 03'
type: DEPARTMENT, PROJECT, CUSTOMER
value_type: REVENUE, COST, MM
value: 12000000, 54000000, *********
*/

Examples:
Q: "Doanh thu lũy kế BU2 là bao nhiêu?"
A: "SELECT
      DATE_FORMAT(CURDATE(), '%m/%Y') AS month,
      entity as department,
      SUM(value) cumulative_revenue
    FROM revenue_cost_static
    WHERE entity = 'BU 02' AND value_type = 'REVENUE' AND YEAR(STR_TO_DATE(month, '%m/%Y')) = YEAR(CURDATE())"

Q: "Doanh thu lũy kế của công ty theo tháng" or "Doanh thu lũy kế của công ty theo từng tháng" or "Lũy kế doanh thu theo tháng"
A: "SELECT DISTINCT
    month,
    SUM(value) OVER (ORDER BY STR_TO_DATE(month, '%m/%Y')) AS cumulative_revenue
FROM revenue_cost_static
WHERE type = 'DEPARTMENT'
    AND value_type = 'REVENUE'
    AND YEAR(STR_TO_DATE(month, '%m/%Y')) = YEAR(CURDATE())
ORDER BY STR_TO_DATE(month, '%m/%Y')"

Q: "Doanh thu lũy kế theo quý"
A: "WITH QuarterlyData AS (
SELECT
	CONCAT (
    'Q',
	QUARTER (STR_TO_DATE (month,
	'%m/%Y')),
	'-',
	YEAR (STR_TO_DATE (month,
	'%m/%Y'))
  ) AS quarter,
	SUM(value) AS quarterly_revenue
FROM
	revenue_cost_static
WHERE
	type = 'DEPARTMENT'
	AND value_type = 'REVENUE'
	AND YEAR(STR_TO_DATE(month, '%m/%Y')) = YEAR(CURDATE())
GROUP BY
	quarter
)
SELECT
	quarter,
	SUM(quarterly_revenue) OVER (
	ORDER BY STR_TO_DATE(SUBSTRING_INDEX(quarter, 'Q', -1), '%m')) AS cumulative_revenue
FROM
	QuarterlyData
ORDER BY
	STR_TO_DATE(SUBSTRING_INDEX(quarter, 'Q', -1), '%m');"

Q: "Doanh thu lũy kế từ đầu năm là bao nhiêu?"
A: "SELECT
      DATE_FORMAT(CURDATE(), '%m/%Y') AS month,
      entity as department,
      SUM(value) cumulative_revenue
    FROM revenue_cost_static
    WHERE type = 'DEPARTMENT' AND value_type = 'REVENUE' AND YEAR(STR_TO_DATE(month, '%m/%Y')) = YEAR(CURDATE())"

Q: "Doanh thu tháng này của BU1 là bao nhiêu? "
A: "SELECT month, entity as department, value as revenue
    FROM revenue_cost_static
    WHERE  entity = 'BU 01'
    AND type = 'DEPARTMENT' AND value_type = 'REVENUE' AND month = DATE_FORMAT(CURRENT_DATE(), '%m/%Y')"

Q: "Lợi nhuận tháng này" or "Lợi nhuận công ty tháng này"
A: "SELECT
        rcs.month,
        SUM(CASE WHEN rcs.value_type = 'REVENUE' THEN rcs.value ELSE 0 END) as revenue,
        SUM(CASE WHEN rcs.value_type = 'COST' THEN rcs.value ELSE 0 END) as cost,
        SUM(CASE WHEN rcs.value_type = 'REVENUE' THEN rcs.value ELSE 0 END) -
        SUM(CASE WHEN rcs.value_type = 'COST' THEN rcs.value ELSE 0 END) AS profit
    FROM
        revenue_cost_static rcs
    WHERE
        rcs.type = 'DEPARTMENT'
        AND rcs.month = DATE_FORMAT(CURDATE(), '%m/%Y')
    GROUP BY
        rcs.month"

Q: "So sánh doanh thu các BU trong 3 tháng gần nhất"
A: "SELECT
            month,
            entity AS deparment,
            value AS revenue
    FROM
            revenue_cost_static rcs
    WHERE
            type = 'DEPARTMENT'
            AND value_type = 'REVENUE'
            AND month BETWEEN
                    DATE_FORMAT(CURRENT_DATE() - INTERVAL 2 MONTH, '%m/%Y')
                    AND DATE_FORMAT(CURRENT_DATE(), '%m/%Y')
            AND YEAR(STR_TO_DATE(month, '%m/%Y')) = YEAR(CURDATE())
    GROUP BY
            deparment,
            month"

Q: "Tháng này công ty có hoàn thành doanh thu theo kế hoạch không?"
A: "SELECT
            rcs.month,
            SUM(CASE WHEN rcp.value_type = 'REVENUE' THEN rcp.value ELSE 0 END) AS plan,
            SUM(CASE WHEN rcs.value_type = 'REVENUE' THEN rcs.value ELSE 0 END) AS revenue,
            ROUND((SUM(CASE WHEN rcs.value_type = 'REVENUE' THEN rcs.value ELSE 0 END) / SUM(CASE WHEN rcp.value_type = 'REVENUE' THEN rcp.value ELSE 0 END) * 100),2) AS completion
    FROM
            revenue_cost_plan rcp
    LEFT JOIN revenue_cost_static rcs ON
            rcp.month = rcs.month
    WHERE
            rcp.type = 'DEPARTMENT'
            AND rcs.type = 'DEPARTMENT'
            AND rcp.value_type = 'REVENUE'
            AND rcs.value_type = 'REVENUE'
            AND rcp.entity_id = rcs.entity_id
            AND rcp.month = DATE_FORMAT(CURRENT_DATE(), '%m/%Y')"
