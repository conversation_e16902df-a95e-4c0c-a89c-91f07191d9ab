const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');

// Route for user registration
router.post('/register', userController.registerUser);

// Route for user login
router.post('/authenticate', userController.loginUser);

// Route for user activate
router.get('/activate/:activationToken', userController.activateAccount);

// Route for user information
router.get('/account', userController.getAccount);

module.exports = router;
