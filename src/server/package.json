{"name": "query-gpt-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "compute-cosine-distance": "^1.0.0", "crypto": "^1.0.1", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "mariadb": "^3.2.2", "mysql2": "^3.6.5", "ngx-clipboard": "^16.0.0", "nodemailer": "^6.9.7", "openai": "^4.14.2", "redis": "^4.6.10", "sequelize": "^6.35.1", "socket.io": "^4.7.2", "tiktoken": "^1.0.10"}}