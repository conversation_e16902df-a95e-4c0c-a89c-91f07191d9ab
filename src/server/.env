PORT=3000
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=12345678
OPENAI_API_KEY=***************************************************
ROLE_CHECK_MODEL=ft:gpt-3.5-turbo-1106:lifesup:spider:8NySK4ei
#QUERY_MODEL=ft:gpt-3.5-turbo-1106:new-digital-sdn-bhd:spider:8IwpSZ0r
#QUERY_MODEL=gpt-3.5-turbo-0613
QUERY_MODEL=ft:gpt-3.5-turbo-1106:lifesup:spider:8NySK4ei
ANSWER_MODEL=ft:gpt-3.5-turbo-1106:lifesup:spider:8NySK4ei
DB_HOST=app.tagon.ai
DB_PORT=3304
DB_USER=querygpt
DB_PASSWORD=tagoninmyheart
DB_CONN_LIMIT=10
DB_NAME=querygpt
ENABLE_ROLE_CHECK=false
SECRET_KEY=f5ae9b4cb1c6d3f28e01a6c9a2487dbb663a06b2a3b4e5d79c9e4823bfc3a907
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=rcxkqpoorgegmpir
EMAIL_FROM=<EMAIL>
BASE_URL=http://localhost
FE_URL=http://app.tagon.ai:8080

