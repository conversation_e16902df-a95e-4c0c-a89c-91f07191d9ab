version: '3'
services:
  querygpt-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: querygpt-server
    restart: always
    ports:
      - "3000:3000"
    environment:
      - QUERY_MODEL=ft:gpt-3.5-turbo-1106:lifesup:spider:8NySK4ei
      - ANSWER_MODEL=ft:gpt-3.5-turbo-1106:lifesup:spider:8NySK4ei
      - ROLE_CHECK_MODEL=ft:gpt-3.5-turbo-1106:lifesup:spider:8NySK4ei
      - REDIS_HOST=querygpt-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=12345678
      - OPENAI_API_KEY=***************************************************
      - DB_HOST=querygpt-db
      - DB_PORT=3306
      - DB_USER=querygpt
      - DB_PASSWORD=tagoninmyheart
      - DB_CONN_LIMIT=10
      - DB_NAME=querygpt
      - ENABLE_ROLE_CHECK=false
      - SECRET_KEY=f5ae9b4cb1c6d3f28e01a6c9a2487dbb663a06b2a3b4e5d79c9e4823bfc3a907
      - EMAIL_HOST=smtp.gmail.com
      - EMAIL_PORT=465
      - EMAIL_SECURE=true
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASSWORD=rcxkqpoorgegmpir
      - EMAIL_FROM=<EMAIL>
      - BASE_URL=http://localhost
      - FE_URL=http://app.tagon.ai:8080
#    networks:
#      - querygpt_server_network
#      - autossl_default
  querygpt-redis:
    image: redis
    container_name: querygpt-redis
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass 12345678
    volumes:
      - "redis_data:/data"
    environment:
      - REDIS_PASSWORD=12345678
    networks:
      - querygpt_server_network
  querygpt-db:
    image: mariadb
    restart: always
    container_name: querygpt-db
    environment:
      MARIADB_USER: querygpt
      MARIADB_PASSWORD: tagoninmyheart
      MARIADB_ROOT_PASSWORD: tagoninmyheart
      MARIADB_DATABASE: querygpt
    ports:
      - "3304:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
    networks:
      - querygpt_server_network
volumes:
  mariadb_data:
  redis_data:
networks:
  querygpt_server_network:
#  autossl_default:
#    external: true
