const express = require("express");
const http = require('http');
const socket = require("socket.io");
const redis = require('redis');
const chatGpt = require('./askChatgpt');
const fs = require('fs');
const path = require('path');
const {authenticateUser} = require('./middleware/authMiddleware');
const sequelize = require('./config/db');

require('dotenv').config();

// App setup
const port = process.env.PORT || 5000
const app = express();
const server = http.createServer(app);
const redisHost = process.env.REDIS_HOST || 'localhost';
const redisPort = process.env.REDIS_PORT || 6379;
const redisPassword = process.env.REDIS_PASSWORD || 6379;
const redisClient = redis.createClient({url: `redis://:${redisPassword}@${redisHost}:${redisPort}`});
const cors = require('cors');

app.use(cors());
redisClient.connect();
// Check if the Redis client is connected
redisClient.on('connect', () => {
  console.log('Connected to Redis');
});

redisClient.on('error', (error) => {
  // console.log('Error connecting to redis: ', error.message);
})

sequelize.sync()
.then(() => {
  console.log('Database synchronized');
  // Start the server or perform other operations
})
.catch((err) => {
  console.error('Error synchronizing database:', err);
});
// Parse application/json
app.use(express.json());

// Parse application/x-www-form-urlencoded
app.use(express.urlencoded({ extended: true }));
app.use((req, res, next) => {
  const shouldSkipAuthentication = (url) => {
    const routePath = url;
    console.log(routePath);
    const skipAuth = ['/api/user/register', '/api/user/authenticate', '/api/user/activate', ''];
    return skipAuth.some((pattern) => {
      // Check if the routePath matches any pattern in skipAuth
      const regex = new RegExp(`^${pattern}(\/.*)?$`); // Allow for additional segments in the URL
      return regex.test(routePath);
    });
  };
  if (shouldSkipAuthentication(req.path)) {
    next(); // Skip the middleware for /register route
  } else {
    authenticateUser(req, res, next); // Apply middleware to other routes
  }
});
// Dynamically load routes from the routes directory
const routesPath = path.join(__dirname, 'routes');
fs.readdirSync(routesPath).forEach((file) => {
  // Skip if it's not a .js file
  if (path.extname(file) === '.js') {
    // Prepare the route
    let route = require(path.join(routesPath, file));

    // Determine the route's prefix (optional)
    let routePrefix = file.replace('.js', '');

    // Use the route with its prefix
    app.use(`/api/${routePrefix}`, route);
  }
});

app.use((req, res, next) => {
  res.status(404).json({ success: false, error: 'Route not found' });
});


// Static files
app.get('/', (req, res) => {
  console.log("hello world")
  res.send("hello world")
});

// Socket setup
const io = socket(server, {
  cors: {
    origin: "*", methods: ["GET", "POST"]
  }
});

io.on("connection", function (socket) {
  console.log("'a user connected'");
  const role = 'CEO';
  const roleCheckModel = process.env.ROLE_CHECK_MODEL || 'gpt-3.5-turbo-0613';
  const enableRoleCheck =  process.env.ENABLE_ROLE_CHECK === 'true';
  const queryModel = process.env.QUERY_MODEL || 'ft:gpt-3.5-turbo-1106:new-digital-sdn-bhd:spider:8IJCDngm';
  const answerModel = process.env.ANSWER_MODEL || 'gpt-3.5-turbo-0613';

  socket.on("disconnect", async () => {
    console.log('Disconnected', socket.roomId);
    const roomId = socket.roomId;
    if (roomId) {
      // TODO clear chat history on disconnect
      await redisClient.del(roomId);
    }
  });

  socket.on("message", async function (message, roomId) {
    function joinRoom(roomId) {
      const rooms = io.sockets.adapter.rooms;
      if (rooms && rooms.get(roomId)) {
        console.log('Socket is in room', roomId);
      } else {
        console.log('Socket is not in room --> join');
        socket.join(roomId);
      }
    }

    joinRoom(roomId);
    // contextMsg.content = contextMsg.content.replaceAll('<ROLE>', role);
    // console.log(JSON.stringify(contextMsg));
    console.log(`Receive question ${message} from user at roomID: ${roomId} `);
    let reply = {};
    console.log('Role checK: ', enableRoleCheck, typeof enableRoleCheck);
    if (enableRoleCheck) {
      console.log('Role check enabled --> call api to check permission!');
      reply = await getPermissionCheck(message, roleCheckModel, roomId + '-rc', role);
    } else {
      console.log('No role check --> all allowed');
      reply.content = 'ALLOWED';
    }
    // Check user's permission
    if (reply.content.startsWith('DENIED')) {
      reply.content = `Xin lỗi, bạn là ${role} và bạn không có quyền để xem nội dung câu trả lời!`;
      io.to(roomId).emit('message', reply);
      await pushIntoRedis(roomId + '-wq', message);
      await pushIntoRedis(roomId + '-wq', reply);
      return;
    } else if (reply.content.startsWith('ALLOWED')) {
      reply = await getSQLQuery(message, queryModel, roomId + '-wq');
    }
    // Write query and form answer
    if (reply.content.startsWith('NOT_FOUND') || !reply.content.includes('SELECT')) {
      reply.content = "Xin lỗi, tôi không được huấn luyện để trả lời câu hỏi như thế này.";
      io.to(roomId).emit('message', reply);
      await pushIntoRedis(roomId, message);
      await pushIntoRedis(roomId, reply);
      return;
    } else {
      reply = await getAnswer(message, answerModel, roomId, reply);
    }
    console.log('reply: ', reply);
    io.to(roomId).emit('message', reply);
  });

  // Handle room creation
  socket.on('create room', async (roomId) => {
    console.log('Created room with id: ', roomId);
    socket.join(roomId);
    socket.roomId = roomId;

    socket.on("typing", function (data) {
      socket.broadcast.emit("typing", data);
    });
  });
});

async function getPermissionCheck(message, model, roomId, role) {
  const role_permission = {
    "CEO": ["COMPANY", "BU1", "BU2", "BU3", "UNKNOWN"],
    "BU1 manager": ["BU1"],
    "BU2 manager": ["BU2"],
    "BU3 manager": ["BU3"],
    "employee": [],
  }
  const contextMsg = await getContextMsg('./sys_prompt/sys_content_check.txt');
  let messages = (await getChatHistory(roomId)).map((chat) => JSON.parse(chat));
  messages = [contextMsg, ...messages, message];
  const reply = await chatGpt.ask(model, messages);
  reply.content = reply.content.split(', ').filter(element => !(new Set(role_permission[role])).has(element)).length === 0 ? "ALLOWED" : "DENIED";
  await pushIntoRedis(roomId, message);
  await pushIntoRedis(roomId, reply);
  return reply
}

async function getSQLQuery(message, model, roomId) {
  const contextMsg = await getContextMsg('./sys_prompt/system_v3.txt');
  // const contextMsg = "Given an input question, use MySQL syntax to generate a sql query by choosing one or multiple of the following tables.\\n\\nNOTE:\\n1. If the database includes relevant information to the question, write the query, otherwise write \\\\'NOT FOUND\\\\' and return your answer immediately;\\n2. If the question is a yes/no question, write the query to display the details (E.g. the percentage of sth) needed instead of NULL/NOT NULL to answer the question;\\n3. If the question is about summarizing all statistical info of sth, write the query with the sum, avg, min, max as extra info.\\n\\n### Database content ###\\nCREATE TABLE `revenue_cost_static` (\\n    `id` bigint(20) NOT NULL AUTO_INCREMENT,\\n    `month` varchar(100) NOT NULL,\\n    `entity_id` bigint(20) NOT NULL,\\n    `entity` varchar(255) NOT NULL,\\n    `type` varchar(255) NOT NULL,\\n    `value_type` varchar(100) NOT NULL,\\n    `value` double NOT NULL,\\n    PRIMARY KEY (`id`)\\n)\\n/* Columns in revenue_cost_static and all categories for low cardinality columns :\\nmonth: 01/2023, 02/2023\\nentity_id: 1,2,3\\nentity: 'BU 01', 'BU 02', 'BU 03'\\ntype: DEPARTMENT, PROJECT, CUSTOMER\\nvalue_type: REVENUE, COST, MM\\nvalue: 12000000, 54000000, 324023132\\n*/\\n\\nCREATE TABLE `revenue_cost_plan` (\\n    `id` bigint(11) NOT NULL AUTO_INCREMENT,\\n    `month` varchar(100) NOT NULL,\\n    `entity_id` bigint(20) NOT NULL,\\n    `entity` varchar(255) NOT NULL,\\n    `type` varchar(100) NOT NULL,\\n    `value_type` varchar(255) NOT NULL,\\n    `value` double NOT NULL,\\n    PRIMARY KEY (`id`)\\n)\\n/* Columns in revenue_cost_plan and all categories for low cardinality columns :\\nmonth: 01/2023, 02/2023\\nentity_id: 1,2,3\\nentity: 'BU 01', 'BU 02', 'BU 03'\\ntype: DEPARTMENT, PROJECT, CUSTOMER\\nvalue_type: REVENUE, COST, MM\\nvalue: 12000000, 54000000, 324023132\\n*/\\n"
  let messages = (await getChatHistory(roomId)).map((chat) => JSON.parse(chat));
  messages = [contextMsg, ...messages, message];
  const reply = (await chatGpt.ask(model, messages));
  reply.content = reply.content.replaceAll('`', '');
  await pushIntoRedis(roomId, message);
  await pushIntoRedis(roomId, reply);
  return reply
}

async function getAnswer(message, model, roomId, reply) {
  reply.data = await chatGpt.queryDB(reply.content);
  console.log('reply.data: ', reply.data);
  if (reply.data) {
    const answerObjStr = await chatGpt.formAnswer(model, {
      question: message.content, data: reply.data
    });
    if (isJSONString(answerObjStr)) {
      const answerObj = JSON.parse(answerObjStr);
      if ('answer' in answerObj && 'chartName' in answerObj) {
        reply.answer = answerObj.answer;
        reply.config = {nameChart: answerObj.chartName, chartType: 'column'};
      }
    } else {
      reply.answer = answerObjStr;
      reply.config = {nameChart: 'Dynamic Chart', chartType: 'column'};
    }
  } else {
    reply.answer = 'The generated SQL has some syntax error...'
  }
  await pushIntoRedis(roomId, message);
  await pushIntoRedis(roomId, {'role': 'assistant', 'content': reply.answer});
  return reply
}

async function getChatHistory(roomId) {
  try {
    return redisClient.lRange(roomId, 0, -1);
  } catch (e) {
    console.log('Error getChatHistory', e.message);
  }
}

async function readFileAsync(fileName) {
  try {
    const dataAsync = await fs.promises.readFile(fileName, {encoding: 'utf8'});
    return dataAsync;
  } catch (error) {
    console.error('Error reading file asynchronously:', error);
  }
}

async function getContextMsg(fileName) {
  const context = await readFileAsync(fileName);
  const cleanedContext = context.replace(/\r/g, ''); // Remove all \r
  return {
    role: 'system', content: cleanedContext
  };
}

async function pushIntoRedis(roomId, message) {
  try {
    await redisClient.rPush(roomId, JSON.stringify(message)); // push into redis
  } catch (e) {
    console.log('Error saving message to redis', e.message);
  }
}

function isJSONString(str) {
  try {
    JSON.parse(str);
    return true;
  } catch (error) {
    return false;
  }
}

server.listen(port, () => {
  console.log(`Server listening on *:${port}`);
});
