# Define the Dockerfile, image name, and repository name
$dockerfile = "Dockerfile-local"
$image_name = "embed-ui-vtt"
$repository_name = "binhpn/embed-ui-vtt"
$environment_file = "src/environments/environment.prod.ts"

# Define the new value to set for CHAT_ENDPOINT
$new_value = "CHAT_ENDPOINT: 'https://cs-vtt-server.querygpt.io'"

# Replace the CHAT_ENDPOINT line in the environment file
(Get-Content $environment_file) | ForEach-Object {
    if ($_ -match "CHAT_ENDPOINT:") {
        $new_value
    } else {
        $_
    }
} | Set-Content $environment_file

# Build the Angular application
npm run build

# Check if the build was successful
if ($LASTEXITCODE -eq 0) {
    Write-Host "Angular application has been successfully built."

    # Build the Docker image
    docker build -f $dockerfile -t $image_name .

    # Check if the Docker build was successful
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Docker image '$image_name' has been successfully built from '$dockerfile'."

        # Tag the image with the repository name
        docker tag $image_name $repository_name

        # Log in to Docker Hub (You will be prompted for credentials)
        docker login

        # Push the image to Docker Hub
        docker push $repository_name

        # Check if the push was successful
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Docker image '$image_name' has been successfully pushed to '$repository_name' on Docker Hub."
        } else {
            Write-Host "Error: Docker image push to '$repository_name' failed."
        }
    } else {
        Write-Host "Error: Docker image build failed."
    }
} else {
    Write-Host "Error: Angular application build failed."
}
