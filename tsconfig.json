/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {"paths": {
    "@shared/*": ["app/chart-component/shared/*"],
    "@core/*": ["app/chart-component/core/*"]
  },

    "allowSyntheticDefaultImports": true,
    "baseUrl": "./src",
    "outDir": "./dist/ROOT",
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "lib": [
      "ES2022",
      "dom"
    ]
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": true
  }
}
