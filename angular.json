{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"query-gpt": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/query-gpt", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/logo.ico", {"glob": "**/*", "input": "src/assets/", "output": "assets/"}], "styles": ["src/styles.scss"], "scripts": [], "allowedCommonJsDependencies": ["highcharts", "sql-highlight", "nearley", "moment"]}, "configurations": {"production": {"optimization": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "labs": {"optimization": true, "outputHashing": "all", "sourceMap": false, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "4mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.labs.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "query-gpt:build:production"}, "development": {"browserTarget": "query-gpt:build:development", "proxyConfig": "proxy.conf.json"}, "labs": {"browserTarget": "query-gpt:build:labs"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "query-gpt:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/logo.ico", {"glob": "**/*", "input": "src/assets/", "output": "assets/"}], "styles": ["src/styles.scss"], "scripts": ["./node_modules/gojs/release/go.js"]}}}}}, "cli": {"analytics": false}}