stages:
  - install-dependencies-embed
  - install-dependencies-embed-prod
  - install-dependencies-embed-labs
  - build-embed
  - build-embed-prod
  - build-embed-labs
  - deploy-to-embed
  - deploy-to-embed-prod
  - deploy-to-embed-labs

variables:
  GIT_DEPTH: 1

default:
  before_script:
    - export NVM_DIR="$HOME/.nvm"
    - source "$NVM_DIR/nvm.sh"
    - nvm use 18

install-dependencies-embed-job:
  stage: install-dependencies-embed
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
    policy: push
  script:
    - npm install
  tags:
    - EMBED
  only:
    refs:
      - embed-api
    changes:
      - package-lock.json
      - package.json

install-dependencies-embed-prod-job:
  stage: install-dependencies-embed-prod
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
    policy: push
  script:
    - npm install
  tags:
    - EMBED-PROD
  only:
    refs:
      - embed-api-prod
    changes:
      - package-lock.json
      - package.json

install-dependencies-embed-labs-job:
  stage: install-dependencies-embed-labs
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
    policy: push
  script:
    - npm install
  tags:
    - EMBED
  only:
    refs:
      - embed-api-labs
    changes:
      - package-lock.json
      - package.json

build-embed-job:
  stage: build-embed
  cache:
      key: ${CI_COMMIT_REF_SLUG} 
      paths: 
        - ./node_modules
      policy: pull
  script:
    #- echo "$CI_embed_ENV_FILE" > env/.env.embedelopment
    - npm run build:dev
  tags:
    - EMBED
  only:
    - embed-api
  artifacts:
    expire_in: 10m
    paths:
      - dist/query-gpt/

build-embed-prod-job:
  stage: build-embed-prod
  cache:
      key: ${CI_COMMIT_REF_SLUG} 
      paths: 
        - ./node_modules
      policy: pull
  script:
    #- echo "$CI_embed_ENV_FILE" > env/.env.embedelopment
    - npm run build
  tags:
    - EMBED-PROD
  only:
    - embed-api-prod
  artifacts:
    expire_in: 10m
    paths:
      - dist/query-gpt/

build-embed-labs-job:
  stage: build-embed-labs
  cache:
      key: ${CI_COMMIT_REF_SLUG} 
      paths: 
        - ./node_modules
      policy: pull
  script:
    #- echo "$CI_embed_ENV_FILE" > env/.env.embedelopment
    - npm run build:labs
  tags:
    - EMBED
  only:
    - embed-api-labs
  artifacts:
    expire_in: 10m
    paths:
      - dist/query-gpt/

deploy-to-embed-job:
  stage: deploy-to-embed
  before_script:
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
  script:
    - ssh-add <(echo "$CI_EMBED_SSH_KEY")
    - ssh -v $CI_EMBED_USER@$CI_EMBED_IP 'rm -rf /var/www/html/query-gpt/*' 2>&1
    - rsync -ahrvz dist/query-gpt/* $CI_EMBED_USER@$CI_EMBED_IP:$CI_EMBED_SOURCE_PATH
  tags:
    - EMBED
  only:
    - embed-api

deploy-to-embed-prod-job:
  stage: deploy-to-embed-prod
  before_script:
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
  script:
    - ssh-add <(echo "$CI_EMBED_PROD_SSH_KEY")
    - ssh -v $CI_EMBED_PROD_USER@$CI_EMBED_PROD_IP 'rm -rf /var/www/html/query-gpt/*' 2>&1
    - rsync -ahrvz dist/query-gpt/* $CI_EMBED_PROD_USER@$CI_EMBED_PROD_IP:$CI_EMBED_PROD_SOURCE_PATH
  tags:
    - EMBED-PROD
  only:
    - embed-api-prod

deploy-to-embed-labs-job:
  stage: deploy-to-embed-labs
  before_script:
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
  script:
    - set -e
    - ssh-add <(echo "$CI_EMBED_LABS_SSH_KEY")
    - |
      while read IP; do
        sleep 20
        ssh -nv $CI_EMBED_LABS_USER@$IP "rm -rf /var/www/html/query-gpt-labs/*" 2>&1
        rsync -ahrvz dist/query-gpt/* $CI_EMBED_LABS_USER@$IP:$CI_EMBED_LABS_SOURCE_PATH
        if [ $? -ne 0 ]; then
          exit 1
        fi
      done < $CI_EMBED_LABS_IP_LIST

  tags:
    - EMBED
  only:
    - embed-api-labs
