FROM nginx:latest

# Set the working directory to NGINX's web content directory
WORKDIR /usr/share/nginx/html

# Remove default NGINX static files
RUN rm -rf ./*

RUN apt-get update

RUN apt-get install -y iputils-ping
RUN apt-get install -y telnet

# Copy the production-ready Angular app from the "dist/query-gpt" directory
COPY dist/query-gpt/. .

COPY nginx-app.conf /etc/nginx/conf.d/default.conf
# Expose port 80 for NGINX
EXPOSE 80

# Start NGINX when the container is run
CMD ["nginx", "-g", "daemon off;"]
