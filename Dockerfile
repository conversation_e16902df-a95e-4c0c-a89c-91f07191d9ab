# Stage 1: Build the Angular app
FROM node:16.14.2 AS build
LABEL authors="binh.phamnguyen"

# Set the working directory in the build container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to install dependencies
COPY package*.json ./

# Install Node.js dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Build the Angular app
RUN npm run build

# Stage 2: Copy the production-ready app into NGINX
FROM nginx:latest

# Set the working directory to NGINX's web content directory
WORKDIR /usr/share/nginx/html

# Remove default NGINX static files
RUN rm -rf ./*

RUN apt-get update

RUN apt-get install -y iputils-ping
RUN apt-get install -y telnet

# Copy the production-ready Angular app from the build container
COPY --from=build /usr/src/app/dist/query-gpt .
COPY nginx-app.conf /etc/nginx/conf.d/default.conf
# Expose port 80 for NGINX
EXPOSE 80

# Start NGINX when the container is run
CMD ["nginx", "-g", "daemon off;"]
