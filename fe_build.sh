#!/bin/sh
git pull
# docker-compose down
# docker-compose up -d --no-deps --build querygpt

# Set the image name and tag
IMAGE_NAME="embed-ui"
TAG="latest"

# Build the Docker image
docker build --no-cache -t "${IMAGE_NAME}:${TAG}" .

# Check if the build was successful
if [ $? -eq 0 ]; then
  echo "Docker image '${IMAGE_NAME}:${TAG}' successfully built."
  docker-compose down
  docker-compose up -d
else
  echo "Failed to build Docker image."
  exit 1
fi

