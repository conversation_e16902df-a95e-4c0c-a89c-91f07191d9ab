{"name": "query-gpt", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration=production", "build:labs": "ng build --configuration=labs", "build:dev": "ng build --configuration=development", "watch": "ng build --watch --configuration development", "test": "ng test", "uglify": "npm i -g uglify-js && uglifyjs src/assets/loader/loader.js -o src/assets/loader/loader.min.js", "minify-css": "uglifycss src/assets/loader/loader.css > src/assets/loader/loader.min.css"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^16.2.11", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "^16.2.11", "@angular/material-moment-adapter": "^16.2.11", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@auth0/angular-jwt": "^5.2.0", "@types/crypto-js": "^4.2.2", "@types/socket.io": "^3.0.2", "crypto-js": "^4.2.0", "gojs": "^2.3.11", "highcharts": "^11.2.0", "lodash-es": "^4.17.21", "ngx-mat-select-search": "^7.0.5", "ngx-scrollbar": "^13.0.2", "rxjs": "~7.8.0", "socket.io-client": "^4.7.2", "sql-formatter": "^13.0.4", "sql-highlight": "^4.4.0", "tslib": "^2.3.0", "uglify-js": "^3.17.4", "uglifycss": "^0.0.29", "uuid": "^9.0.1", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.8", "@angular/cli": "^16.2.8", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "@types/jsonwebtoken": "^9.0.6", "@types/uuid": "^9.0.6", "autoprefixer": "^10.4.16", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "moment": "^2.29.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "~5.1.3"}}